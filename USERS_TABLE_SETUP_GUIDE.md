# DODOBOOKER Users Table Setup Guide

## 🎯 Overview
This guide will help you create the `users` table in Supabase and ensure that user registration automatically creates user profiles.

## 📋 What This Setup Includes

✅ **Users table** with all required fields  
✅ **Automatic user profile creation** via database triggers  
✅ **Row Level Security (RLS)** policies  
✅ **Proper indexes** for performance  
✅ **Updated auth service** to handle user registration  

## 🚀 Step-by-Step Setup

### Step 1: Create the Users Table in Supabase

1. **Open your Supabase dashboard**: https://supabase.com/dashboard/project/yzlkkjejmjvqtgvtdsnk
2. **Go to SQL Editor** (left sidebar)
3. **Create a new query**
4. **Copy and paste** the entire content from `supabase_users_table_setup.sql`
5. **Click "Run"** to execute the script

### Step 2: Verify the Setup

After running the SQL script, verify that:

1. **Users table exists**: Go to Table Editor → `users` table should be visible
2. **Trigger function exists**: The `handle_new_user()` function should be created
3. **RLS policies are active**: Check Authentication → Policies → `users` table

### Step 3: Test User Registration

1. **Run your Flutter app**: `flutter run -d chrome`
2. **Try to register a new user** through your app
3. **Check the users table** in Supabase to see if the user profile was created automatically

## 🔧 How It Works

### Automatic User Profile Creation

When a user registers:

1. **Supabase Auth** creates a user in `auth.users` table
2. **Database trigger** automatically fires
3. **User profile** is created in `public.users` table with data from user metadata
4. **Your app** can now access user data from the `users` table

### Database Trigger Function

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, name, phone_number, phone)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'name',
    NEW.raw_user_meta_data->>'phone_number',
    NEW.raw_user_meta_data->>'phone'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Updated Auth Service

The `AuthService` now:
- ✅ **Logs registration progress** for debugging
- ✅ **Verifies user profile creation** after registration
- ✅ **Creates profile manually** if trigger fails
- ✅ **Fixes DateTime serialization** issues

## 🔒 Security Features

### Row Level Security (RLS)

The users table has RLS enabled with these policies:

1. **Read own data**: Users can only read their own profile
2. **Update own data**: Users can only update their own profile  
3. **Insert on registration**: Authenticated users can create their profile

### Data Protection

- ✅ **Foreign key constraint** links to `auth.users`
- ✅ **Cascade delete** removes profile when auth user is deleted
- ✅ **Status validation** ensures valid status values
- ✅ **Unique email** constraint prevents duplicates

## 🧪 Testing

### Test Registration Flow

1. **Register a new user** with:
   - Name: "Test User"
   - Email: "<EMAIL>"
   - Phone: "+1234567890"

2. **Check the users table** in Supabase:
   ```sql
   SELECT * FROM users WHERE email = '<EMAIL>';
   ```

3. **Verify all fields** are populated correctly

### Test Login Flow

1. **Login with the test user**
2. **Check console logs** for successful profile retrieval
3. **Verify last_login** is updated in the users table

## 🐛 Troubleshooting

### Common Issues

**Issue**: User profile not created automatically
- **Solution**: Check if the trigger exists and is enabled
- **Check**: Run `SELECT * FROM pg_trigger WHERE tgname = 'on_auth_user_created';`

**Issue**: RLS policy errors
- **Solution**: Ensure policies are created correctly
- **Check**: Go to Authentication → Policies in Supabase dashboard

**Issue**: DateTime serialization errors
- **Solution**: Already fixed in updated auth service (converts to ISO string)

### Debug Registration

The auth service now includes detailed logging:
```
🔄 Starting user registration for: <EMAIL>
✅ Auth user created: uuid-here
✅ User profile verified/created in users table
```

## 📊 Database Schema

### Users Table Structure

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, links to auth.users |
| `email` | varchar(255) | User email (unique) |
| `full_name` | varchar(255) | User's full name |
| `name` | varchar(128) | Display name |
| `phone_number` | varchar(32) | Phone number |
| `phone` | varchar(32) | Alternative phone field |
| `avatar_url` | text | Profile picture URL |
| `status` | varchar(20) | active/inactive/suspended |
| `last_login` | timestamptz | Last login time |
| `created_at` | timestamptz | Account creation time |
| `updated_at` | timestamptz | Last update time |

## ✅ Success Checklist

- [ ] SQL script executed successfully
- [ ] Users table created with all fields
- [ ] Trigger function created
- [ ] RLS policies active
- [ ] Auth service updated
- [ ] Test user registration works
- [ ] User profile appears in users table
- [ ] Login updates last_login field

## 🎉 Next Steps

After completing this setup:

1. **Test the complete flow** with real user registration
2. **Add user profile editing** functionality to your app
3. **Implement user avatar upload** if needed
4. **Add user role management** if required

Your users table is now ready for production use! 🚀
