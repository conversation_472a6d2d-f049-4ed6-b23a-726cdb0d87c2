-- Sample data for DODOBOOKER home services app

-- Insert Categories
INSERT INTO category (id, name, image, featured, status, created_at, updated_at, priority, start_time, end_time) VALUES
(1, 'Cleaning Services', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', NOW(), NOW(), 1, '08:00:00', '20:00:00'),
(2, 'Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', NOW(), NOW(), 2, '09:00:00', '18:00:00'),
(3, 'Electrical', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', NOW(), NOW(), 3, '09:00:00', '17:00:00'),
(4, 'Carpentry', 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=300&h=300&fit=crop', false, 'active', NOW(), NOW(), 4, '08:00:00', '18:00:00'),
(5, 'Painting', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', NOW(), NOW(), 5, '08:00:00', '17:00:00'),
(6, 'Appliance Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', NOW(), NOW(), 6, '10:00:00', '19:00:00');

-- Insert Subcategories
INSERT INTO "Subcategory" (id, name, image, featured, status, category_id, created_at, updated_at) VALUES
-- Cleaning Services subcategories
(1, 'House Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, NOW(), NOW()),
(2, 'Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', false, 'active', 1, NOW(), NOW()),
(3, 'Deep Cleaning', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=300&fit=crop', true, 'active', 1, NOW(), NOW()),

-- Plumbing subcategories
(4, 'Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, NOW(), NOW()),
(5, 'Drain Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 2, NOW(), NOW()),
(6, 'Fixture Installation', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, NOW(), NOW()),

-- Electrical subcategories
(7, 'Wiring', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3, NOW(), NOW()),
(8, 'Switch & Outlet', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3, NOW(), NOW());

-- Insert SubSubCategories
INSERT INTO "SubSubCategory" (id, name, image, featured, status, category_id, subcategory_id, created_at, updated_at) VALUES
-- House Cleaning subsubcategories
(1, 'Regular Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 1, NOW(), NOW()),
(2, 'Move-in Cleaning', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=300&fit=crop', false, 'active', 1, 1, NOW(), NOW()),
(3, 'Post-construction Cleaning', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=300&fit=crop', false, 'active', 1, 1, NOW(), NOW()),

-- Deep Cleaning subsubcategories
(4, 'Kitchen Deep Clean', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop', true, 'active', 1, 3, NOW(), NOW()),
(5, 'Bathroom Deep Clean', 'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?w=300&h=300&fit=crop', true, 'active', 1, 3, NOW(), NOW()),

-- Pipe Repair subsubcategories
(6, 'Leak Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 4, NOW(), NOW()),
(7, 'Pipe Replacement', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, 4, NOW(), NOW());

-- Insert Products
INSERT INTO "Products" (id, product_name, category_id, subcategory_id, sub_subcategory_id, status, created_at, updated_at, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage, discount_type, flat_discount_amount, after_discount_price) VALUES
-- Regular Cleaning products
(1, 'Basic House Cleaning', 1, '1', '1', 'active', NOW(), NOW(), 2500, 'Complete house cleaning including dusting, mopping, and vacuuming', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 120, 2, 20, 'percentage', '0', 2000.0),
(2, 'Premium House Cleaning', 1, '1', '1', 'active', NOW(), NOW(), 4000, 'Premium cleaning service with eco-friendly products', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=300&fit=crop', true, 180, 3, 15, 'percentage', '0', 3400.0),

-- Move-in Cleaning products
(3, 'Move-in Deep Clean', 1, '1', '2', 'active', NOW(), NOW(), 5000, 'Thorough cleaning for new homes', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=300&fit=crop', false, 240, 4, 0, 'percentage', '0', NULL),

-- Kitchen Deep Clean products
(4, 'Kitchen Deep Cleaning', 1, '3', '4', 'active', NOW(), NOW(), 1500, 'Deep cleaning of kitchen including appliances', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop', true, 90, 2, 25, 'percentage', '0', 1125.0),
(5, 'Kitchen & Appliance Clean', 1, '3', '4', 'active', NOW(), NOW(), 2200, 'Complete kitchen and appliance deep cleaning', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop', false, 150, 3, 0, 'percentage', '0', NULL),

-- Bathroom Deep Clean products
(6, 'Bathroom Deep Clean', 1, '3', '5', 'active', NOW(), NOW(), 1200, 'Complete bathroom sanitization and cleaning', 'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?w=300&h=300&fit=crop', true, 60, 1, 30, 'percentage', '0', 840.0),

-- Plumbing products
(7, 'Pipe Leak Repair', 2, '4', '6', 'active', NOW(), NOW(), 1800, 'Professional pipe leak detection and repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 90, 1, 0, 'percentage', '0', NULL),
(8, 'Emergency Leak Fix', 2, '4', '6', 'active', NOW(), NOW(), 2500, '24/7 emergency leak repair service', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 60, 1, 10, 'percentage', '0', 2250.0),

-- Pipe Replacement products
(9, 'Pipe Replacement Service', 2, '4', '7', 'active', NOW(), NOW(), 3500, 'Complete pipe replacement with warranty', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 180, 2, 0, 'percentage', '0', NULL),

-- Electrical products
(10, 'Home Wiring Service', 3, '7', NULL, 'active', NOW(), NOW(), 4500, 'Complete home electrical wiring', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 300, 4, 0, 'percentage', '0', NULL),
(11, 'Switch Installation', 3, '8', NULL, 'active', NOW(), NOW(), 800, 'Professional switch and outlet installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 45, 1, 20, 'percentage', '0', 640.0);
