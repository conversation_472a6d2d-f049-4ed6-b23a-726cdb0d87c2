-- DODOBOOKER Complete Database Setup with Sample Data
-- Run this in your Supabase SQL Editor

-- 1. Create trigger function for updating updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Create Categories table
CREATE TABLE IF NOT EXISTS public.category (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  priority integer DEFAULT 0,
  start_time time without time zone DEFAULT '09:00:00'::time without time zone,
  end_time time without time zone DEFAULT '17:00:00'::time without time zone,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  CONSTRAINT category_pkey PRIMARY KEY (id),
  CONSTRAINT category_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text]))
);

-- 3. Create Subcategories table
CREATE TABLE IF NOT EXISTS public."Subcategory" (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status text NULL,
  CONSTRAINT subcategory_pkey PRIMARY KEY (id),
  CONSTRAINT subcategory_category_id_fkey FOREIGN KEY (category_id) REFERENCES category (id) ON DELETE CASCADE,
  CONSTRAINT subcategory_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text])),
  CONSTRAINT unique_subcategory_name_per_category UNIQUE (name, category_id)
);

-- 4. Create SubSubCategories table
CREATE TABLE IF NOT EXISTS public."SubSubCategory" (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status text NULL,
  CONSTRAINT subsubcategory_pkey PRIMARY KEY (id),
  CONSTRAINT subsubcategory_category_id_fkey FOREIGN KEY (category_id) REFERENCES category (id) ON DELETE CASCADE,
  CONSTRAINT subsubcategory_subcategory_id_fkey FOREIGN KEY (subcategory_id) REFERENCES "Subcategory" (id) ON DELETE CASCADE,
  CONSTRAINT subsubcategory_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text])),
  CONSTRAINT unique_subsubcategory_name_per_subcategory UNIQUE (name, subcategory_id)
);

-- 5. Create Products table
CREATE TABLE IF NOT EXISTS public."Products" (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  product_name text NOT NULL,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  sub_subcategory_id bigint NOT NULL,
  status text DEFAULT 'active'::text,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  price bigint NOT NULL DEFAULT 0,
  description text NULL,
  image text NULL,
  featured boolean DEFAULT false,
  previous_status text NULL,
  duration_minutes bigint DEFAULT 60,
  number_of_slots bigint DEFAULT 1,
  disabled_start_at timestamp with time zone NULL,
  disabled_end_at timestamp with time zone NULL,
  discount_percentage bigint DEFAULT 0,
  discount_type text DEFAULT 'percentage'::text,
  flat_discount_amount bigint DEFAULT 0,
  after_discount_price double precision NULL,
  
  CONSTRAINT products_pkey PRIMARY KEY (id),
  CONSTRAINT products_category_id_fkey FOREIGN KEY (category_id) REFERENCES category (id) ON DELETE CASCADE,
  CONSTRAINT products_subcategory_id_fkey FOREIGN KEY (subcategory_id) REFERENCES "Subcategory" (id) ON DELETE CASCADE,
  CONSTRAINT products_sub_subcategory_id_fkey FOREIGN KEY (sub_subcategory_id) REFERENCES "SubSubCategory" (id) ON DELETE CASCADE,
  CONSTRAINT products_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text, 'out_of_stock'::text])),
  CONSTRAINT products_discount_type_check CHECK (discount_type = ANY (ARRAY['percentage'::text, 'flat'::text, 'none'::text])),
  CONSTRAINT products_price_positive CHECK (price >= 0),
  CONSTRAINT products_discount_percentage_valid CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
  CONSTRAINT products_flat_discount_positive CHECK (flat_discount_amount >= 0),
  CONSTRAINT products_duration_positive CHECK (duration_minutes > 0),
  CONSTRAINT products_slots_positive CHECK (number_of_slots > 0),
  CONSTRAINT unique_product_name_per_subsubcategory UNIQUE (product_name, sub_subcategory_id)
);

-- 6. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_category_status ON category USING btree (status);
CREATE INDEX IF NOT EXISTS idx_category_featured ON category USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_category_priority ON category USING btree (priority);

CREATE INDEX IF NOT EXISTS idx_subcategory_category_id ON "Subcategory" USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subcategory_status ON "Subcategory" USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subcategory_featured ON "Subcategory" USING btree (featured);

CREATE INDEX IF NOT EXISTS idx_subsubcategory_category_id ON "SubSubCategory" USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_subcategory_id ON "SubSubCategory" USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_status ON "SubSubCategory" USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_featured ON "SubSubCategory" USING btree (featured);

CREATE INDEX IF NOT EXISTS idx_products_category_id ON "Products" USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_products_subcategory_id ON "Products" USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_products_sub_subcategory_id ON "Products" USING btree (sub_subcategory_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON "Products" USING btree (status);
CREATE INDEX IF NOT EXISTS idx_products_featured ON "Products" USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_products_price ON "Products" USING btree (price);

-- 7. Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_category_updated_at ON category;
CREATE TRIGGER update_category_updated_at BEFORE UPDATE ON category FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subcategory_updated_at ON "Subcategory";
CREATE TRIGGER update_subcategory_updated_at BEFORE UPDATE ON "Subcategory" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subsubcategory_updated_at ON "SubSubCategory";
CREATE TRIGGER update_subsubcategory_updated_at BEFORE UPDATE ON "SubSubCategory" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_products_updated_at ON "Products";
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON "Products" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. Create discount calculation trigger for products
CREATE OR REPLACE FUNCTION calculate_after_discount_price()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.discount_type = 'percentage' AND NEW.discount_percentage > 0 THEN
    NEW.after_discount_price = NEW.price * (1 - NEW.discount_percentage / 100.0);
  ELSIF NEW.discount_type = 'flat' AND NEW.flat_discount_amount > 0 THEN
    NEW.after_discount_price = GREATEST(0, NEW.price - NEW.flat_discount_amount);
  ELSE
    NEW.after_discount_price = NEW.price;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS calculate_discount_price ON "Products";
CREATE TRIGGER calculate_discount_price BEFORE INSERT OR UPDATE ON "Products" FOR EACH ROW EXECUTE FUNCTION calculate_after_discount_price();

-- 9. Enable RLS and create policies
ALTER TABLE category ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Subcategory" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "SubSubCategory" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Products" ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON category;
CREATE POLICY "Allow public read access" ON category FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow public read access" ON "Subcategory";
CREATE POLICY "Allow public read access" ON "Subcategory" FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow public read access" ON "SubSubCategory";
CREATE POLICY "Allow public read access" ON "SubSubCategory" FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow public read access" ON "Products";
CREATE POLICY "Allow public read access" ON "Products" FOR SELECT USING (true);

-- 10. Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON category TO anon, authenticated;
GRANT SELECT ON "Subcategory" TO anon, authenticated;
GRANT SELECT ON "SubSubCategory" TO anon, authenticated;
GRANT SELECT ON "Products" TO anon, authenticated;

SELECT 'Database structure created successfully!' as message;
