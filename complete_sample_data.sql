-- DODOBOOKER Complete Sample Data
-- Run this AFTER creating the database structure

-- 1. Insert Categories
INSERT INTO category (id, name, image, featured, status, priority, start_time, end_time) VALUES
(1, 'Cleaning Services', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, '08:00:00', '20:00:00'),
(2, 'Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, '09:00:00', '18:00:00'),
(3, 'Electrical Services', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3, '09:00:00', '17:00:00'),
(4, 'Carpentry', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4, '08:00:00', '18:00:00'),
(5, 'Painting', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', false, 'active', 5, '08:00:00', '17:00:00'),
(6, 'Appliance Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 6, '10:00:00', '19:00:00')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  image = EXCLUDED.image,
  featured = EXCLUDED.featured,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  start_time = EXCLUDED.start_time,
  end_time = EXCLUDED.end_time,
  updated_at = NOW();

-- 2. Insert Subcategories for Cleaning Services (Category ID: 1)
INSERT INTO "Subcategory" (name, image, featured, status, category_id) VALUES
('House Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1),
('Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1),
('Deep Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1),
('Carpet Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1)
ON CONFLICT (name, category_id) DO NOTHING;

-- 3. Insert Subcategories for Plumbing (Category ID: 2)
INSERT INTO "Subcategory" (name, image, featured, status, category_id) VALUES
('Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
('Drain Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
('Faucet Installation', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
('Emergency Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2)
ON CONFLICT (name, category_id) DO NOTHING;

-- 4. Insert Subcategories for Electrical Services (Category ID: 3)
INSERT INTO "Subcategory" (name, image, featured, status, category_id) VALUES
('Wiring Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3),
('Light Fixture Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3),
('Outlet Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
('Emergency Electrical', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3)
ON CONFLICT (name, category_id) DO NOTHING;

-- 5. Insert Subcategories for Carpentry (Category ID: 4)
INSERT INTO "Subcategory" (name, image, featured, status, category_id) VALUES
('Furniture Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4),
('Cabinet Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4),
('Door Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
('Custom Woodwork', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4)
ON CONFLICT (name, category_id) DO NOTHING;

-- 6. Insert SubSubCategories for House Cleaning (Subcategory ID: 1, Category ID: 1)
INSERT INTO "SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
('Regular House Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 1),
('One-Time House Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 1),
('Move-in/Move-out Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1, 1)
ON CONFLICT (name, subcategory_id) DO NOTHING;

-- 7. Insert SubSubCategories for Office Cleaning (Subcategory ID: 2, Category ID: 1)
INSERT INTO "SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
('Daily Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1, 2),
('Weekly Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1, 2),
('Commercial Space Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', false, 'active', 1, 2)
ON CONFLICT (name, subcategory_id) DO NOTHING;

-- 8. Insert SubSubCategories for Pipe Repair (Subcategory ID: 5, Category ID: 2)
INSERT INTO "SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
('Burst Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 5),
('Leaky Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 5),
('Pipe Replacement', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, 5)
ON CONFLICT (name, subcategory_id) DO NOTHING;

-- 9. Insert SubSubCategories for Furniture Assembly (Subcategory ID: 13, Category ID: 4)
INSERT INTO "SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
('IKEA Furniture Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4, 13),
('Bed Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4, 13),
('Desk Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4, 13)
ON CONFLICT (name, subcategory_id) DO NOTHING;

-- 10. Insert Products for Regular House Cleaning (SubSubCategory ID: 1)
INSERT INTO "Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
('Basic House Cleaning - 1 Bedroom', 1, 1, 1, 2500, 'Complete cleaning of 1-bedroom apartment including kitchen, bathroom, living room, and bedroom', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 120, 2, 10),
('Basic House Cleaning - 2 Bedroom', 1, 1, 1, 3500, 'Complete cleaning of 2-bedroom apartment including kitchen, bathroom, living room, and bedrooms', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 180, 2, 15),
('Basic House Cleaning - 3 Bedroom', 1, 1, 1, 4500, 'Complete cleaning of 3-bedroom house including kitchen, bathrooms, living room, and bedrooms', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', false, 240, 3, 20)
ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

-- 11. Insert Products for One-Time House Cleaning (SubSubCategory ID: 2)
INSERT INTO "Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_type, flat_discount_amount) VALUES
('One-Time Deep Clean - Small Home', 1, 1, 2, 5000, 'Intensive one-time cleaning for small homes up to 1000 sq ft', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 300, 3, 'flat', 500),
('One-Time Deep Clean - Medium Home', 1, 1, 2, 7500, 'Intensive one-time cleaning for medium homes 1000-2000 sq ft', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 420, 4, 'flat', 750),
('One-Time Deep Clean - Large Home', 1, 1, 2, 10000, 'Intensive one-time cleaning for large homes over 2000 sq ft', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', false, 540, 5, 'flat', 1000)
ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

-- 12. Insert Products for Burst Pipe Repair (SubSubCategory ID: 7)
INSERT INTO "Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
('Emergency Burst Pipe Repair', 2, 5, 7, 8000, '24/7 emergency burst pipe repair service with immediate response', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', true, 180, 1, 0),
('Burst Pipe Repair with Replacement', 2, 5, 7, 12000, 'Complete burst pipe repair including pipe replacement and testing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', true, 240, 1, 5),
('Minor Burst Pipe Fix', 2, 5, 7, 5000, 'Quick fix for minor burst pipe issues', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', false, 90, 1, 10)
ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

-- 13. Insert Products for IKEA Furniture Assembly (SubSubCategory ID: 10)
INSERT INTO "Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
('IKEA Bed Assembly', 4, 13, 10, 2500, 'Professional assembly of IKEA beds including all types and sizes', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', true, 90, 2, 10),
('IKEA Wardrobe Assembly', 4, 13, 10, 4000, 'Complete assembly of IKEA wardrobes and closet systems', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', true, 150, 2, 15),
('IKEA Kitchen Cabinet Assembly', 4, 13, 10, 6000, 'Professional assembly of IKEA kitchen cabinets and installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', false, 240, 2, 20),
('IKEA Desk Assembly', 4, 13, 10, 1500, 'Quick and efficient assembly of IKEA desks and office furniture', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', false, 60, 3, 5)
ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

-- 14. Verification queries
SELECT 'Sample data insertion completed!' as message;

-- Show summary of inserted data
SELECT 'Categories: ' || COUNT(*) as count FROM category;
SELECT 'Subcategories: ' || COUNT(*) as count FROM "Subcategory";
SELECT 'SubSubCategories: ' || COUNT(*) as count FROM "SubSubCategory";
SELECT 'Products: ' || COUNT(*) as count FROM "Products";

-- Show complete hierarchy
SELECT 
  c.name as category,
  s.name as subcategory,
  ss.name as subsubcategory,
  p.product_name,
  p.price,
  p.after_discount_price
FROM category c
LEFT JOIN "Subcategory" s ON c.id = s.category_id
LEFT JOIN "SubSubCategory" ss ON s.id = ss.subcategory_id
LEFT JOIN "Products" p ON ss.id = p.sub_subcategory_id
WHERE p.id IS NOT NULL
ORDER BY c.priority, s.name, ss.name, p.product_name;
