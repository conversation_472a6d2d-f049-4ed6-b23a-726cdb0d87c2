import 'package:flutter/material.dart';
import '../services/category_service.dart';
import '../models/category.dart';

class DatabaseTestPage extends StatefulWidget {
  const DatabaseTestPage({super.key});

  @override
  State<DatabaseTestPage> createState() => _DatabaseTestPageState();
}

class _DatabaseTestPageState extends State<DatabaseTestPage> {
  List<Category> _categories = [];
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _testDatabaseConnection();
  }

  Future<void> _testDatabaseConnection() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      print('🧪 Testing database connection...');
      final categories = await CategoryService.getCategories();
      
      setState(() {
        _categories = categories;
        _isLoading = false;
      });
      
      print('✅ Database test completed. Found ${categories.length} categories');
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
      print('❌ Database test failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Connection Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connection Info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Supabase Connection',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('URL: https://yzlkkjejmjvqtgvtdsnk.supabase.co'),
                  const SizedBox(height: 4),
                  Text('Table: category'),
                  const SizedBox(height: 4),
                  Text('Status: ${_isLoading ? "Testing..." : "Connected"}'),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Test Button
            ElevatedButton(
              onPressed: _isLoading ? null : _testDatabaseConnection,
              child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Test Connection'),
            ),
            
            const SizedBox(height: 20),
            
            // Error Message
            if (_errorMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            
            // Results
            const Text(
              'Categories Found:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            
            Expanded(
              child: _categories.isEmpty
                ? const Center(
                    child: Text(
                      'No categories found.\nCheck the console for debug information.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: category.image != null && category.image!.isNotEmpty
                            ? CircleAvatar(
                                backgroundImage: NetworkImage(category.image!),
                                onBackgroundImageError: (_, __) {},
                                child: category.image!.isEmpty 
                                  ? Text(category.displayName[0])
                                  : null,
                              )
                            : CircleAvatar(
                                child: Text(category.displayName[0]),
                              ),
                          title: Text(category.displayName),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('ID: ${category.id}'),
                              Text('Status: ${category.status ?? "null"}'),
                              Text('Priority: ${category.priority ?? "null"}'),
                              if (category.featured == true)
                                const Text(
                                  'Featured',
                                  style: TextStyle(
                                    color: Colors.orange,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                            ],
                          ),
                          trailing: category.image != null && category.image!.isNotEmpty
                            ? const Icon(Icons.image, color: Colors.green)
                            : const Icon(Icons.image_not_supported, color: Colors.grey),
                        ),
                      );
                    },
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
