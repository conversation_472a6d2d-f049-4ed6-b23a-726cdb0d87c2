-- Check current categories in your database
-- Run this in Supabase SQL Editor to see your data

-- 1. Check all categories
SELECT id, name, image, featured, status, priority 
FROM category 
ORDER BY priority ASC, name ASC;

-- 2. Check only active categories (what the app will show)
SELECT id, name, image, featured, status, priority 
FROM category 
WHERE status = 'active'
ORDER BY priority ASC, name ASC;

-- 3. Check if image URLs are valid (not null/empty)
SELECT 
  id, 
  name, 
  CASE 
    WHEN image IS NULL THEN 'NULL'
    WHEN image = '' THEN 'EMPTY'
    WHEN image LIKE 'http%' THEN 'VALID URL'
    ELSE 'INVALID'
  END as image_status,
  image,
  featured,
  status
FROM category 
WHERE status = 'active'
ORDER BY priority ASC;

-- 4. Count categories by status
SELECT status, COUNT(*) as count
FROM category 
GROUP BY status;
