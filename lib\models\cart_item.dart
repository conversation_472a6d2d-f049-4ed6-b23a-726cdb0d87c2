import 'product.dart';

class CartItem {
  final Product product;
  int quantity;
  final String? selectedVariant;
  final Map<String, dynamic>? customizations;

  CartItem({
    required this.product,
    this.quantity = 1,
    this.selectedVariant,
    this.customizations,
  });

  double get totalPrice => product.priceAsDouble * quantity;

  Map<String, dynamic> toJson() {
    return {
      'product': product.toJson(),
      'quantity': quantity,
      'selectedVariant': selectedVariant,
      'customizations': customizations,
    };
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      product: Product.fromJson(json['product']),
      quantity: json['quantity'] ?? 1,
      selectedVariant: json['selectedVariant'],
      customizations: json['customizations'],
    );
  }

  CartItem copyWith({
    Product? product,
    int? quantity,
    String? selectedVariant,
    Map<String, dynamic>? customizations,
  }) {
    return CartItem(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      selectedVariant: selectedVariant ?? this.selectedVariant,
      customizations: customizations ?? this.customizations,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.product.id == product.id &&
        other.selectedVariant == selectedVariant;
  }

  @override
  int get hashCode => product.id.hashCode ^ selectedVariant.hashCode;
}
