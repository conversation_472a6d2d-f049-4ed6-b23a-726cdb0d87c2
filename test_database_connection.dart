import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  print('🔄 Starting database connection test...');
  
  try {
    // Initialize Supabase
    await Supabase.initialize(
      url: 'https://yzlkkjejmjvqtgvtdsnk.supabase.co',
      anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl6bGtramVqbWp2cXRndnRkc25rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNDkzODIsImV4cCI6MjA2NzcyNTM4Mn0.bih1P8f_Fi-Ma4Euj0E5BHs0MQS-237YMdzZTIop7Kw',
    );

    final supabase = Supabase.instance.client;
    print('✅ Supabase initialized successfully');

    // Test 1: Check if we can connect to the database
    print('\n🧪 Test 1: Basic connection test');
    try {
      final response = await supabase.from('category').select('count').count(CountOption.exact);
      print('✅ Connection successful! Category table has ${response.count} rows');
    } catch (e) {
      print('❌ Connection failed: $e');
    }

    // Test 2: Try to fetch all categories
    print('\n🧪 Test 2: Fetch all categories');
    try {
      final categories = await supabase.from('category').select('*');
      print('✅ Query successful! Found ${categories.length} categories');
      
      if (categories.isNotEmpty) {
        print('📋 Categories found:');
        for (var category in categories) {
          print('  - ${category['name']} (ID: ${category['id']}, Status: ${category['status']})');
        }
      } else {
        print('⚠️ No categories found in the table');
      }
    } catch (e) {
      print('❌ Query failed: $e');
    }

    // Test 3: Check table structure
    print('\n🧪 Test 3: Check table structure');
    try {
      // Try to get table schema information
      final schema = await supabase.rpc('get_schema_info');
      print('✅ Schema info retrieved: $schema');
    } catch (e) {
      print('❌ Schema check failed: $e');
    }

    // Test 4: Try to insert a test category
    print('\n🧪 Test 4: Try to insert a test category');
    try {
      final insertResponse = await supabase.from('category').insert({
        'name': 'Test Category',
        'image': 'https://example.com/test.jpg',
        'featured': true,
        'status': 'active',
        'priority': 999,
        'start_time': '08:00:00',
        'end_time': '18:00:00',
      }).select();
      
      print('✅ Insert successful: $insertResponse');
      
      // Clean up - delete the test category
      if (insertResponse.isNotEmpty) {
        final testId = insertResponse[0]['id'];
        await supabase.from('category').delete().eq('id', testId);
        print('🧹 Test category cleaned up');
      }
    } catch (e) {
      print('❌ Insert failed: $e');
      
      // Check if it's an RLS issue
      if (e.toString().contains('RLS') || 
          e.toString().contains('policy') || 
          e.toString().contains('permission')) {
        print('🔒 This appears to be a Row Level Security (RLS) issue');
        print('💡 You may need to:');
        print('   1. Disable RLS on the category table, OR');
        print('   2. Create proper RLS policies for anonymous users');
      }
    }

    // Test 5: Check other tables
    print('\n🧪 Test 5: Check other tables');
    final tables = ['Subcategory', 'SubSubCategory', 'Products'];
    
    for (String tableName in tables) {
      try {
        final response = await supabase.from(tableName).select('count').count(CountOption.exact);
        print('✅ $tableName table: ${response.count} rows');
      } catch (e) {
        print('❌ $tableName table error: $e');
      }
    }

    print('\n🎯 Database test completed!');
    
  } catch (e) {
    print('❌ Fatal error during database test: $e');
  }
}
