import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://yzlkkjejmjvqtgvtdsnk.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl6bGtramVqbWp2cXRndnRkc25rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNDkzODIsImV4cCI6MjA2NzcyNTM4Mn0.bih1P8f_Fi-Ma4Euj0E5BHs0MQS-237YMdzZTIop7Kw',
  );

  final supabase = Supabase.instance.client;

  try {
    print('Testing connection to Supabase...');
    
    // Test categories table
    print('Fetching categories...');
    final categoriesResponse = await supabase
        .from('category')
        .select()
        .limit(5);
    
    print('Categories found: ${categoriesResponse.length}');
    for (var category in categoriesResponse) {
      print('- ${category['name']} (ID: ${category['id']})');
    }

    // Test subcategories table
    print('\nFetching subcategories...');
    final subcategoriesResponse = await supabase
        .from('Subcategory')
        .select()
        .limit(5);
    
    print('Subcategories found: ${subcategoriesResponse.length}');
    for (var subcategory in subcategoriesResponse) {
      print('- ${subcategory['name']} (ID: ${subcategory['id']})');
    }

    // Test subsubcategories table
    print('\nFetching subsubcategories...');
    final subsubcategoriesResponse = await supabase
        .from('SubSubCategory')
        .select()
        .limit(5);
    
    print('SubSubCategories found: ${subsubcategoriesResponse.length}');
    for (var subsubcategory in subsubcategoriesResponse) {
      print('- ${subsubcategory['name']} (ID: ${subsubcategory['id']})');
    }

    // Test products table
    print('\nFetching products...');
    final productsResponse = await supabase
        .from('Products')
        .select()
        .limit(5);
    
    print('Products found: ${productsResponse.length}');
    for (var product in productsResponse) {
      print('- ${product['product_name']} (ID: ${product['id']})');
    }

    print('\n✅ Connection successful! All tables are accessible.');
    
  } catch (e) {
    print('❌ Error connecting to database: $e');
  }
}
