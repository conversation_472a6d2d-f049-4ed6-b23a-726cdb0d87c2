import 'package:supabase_flutter/supabase_flutter.dart';

class UserService {
  static SupabaseClient get _supabase => Supabase.instance.client;

  // Get current user profile
  static Future<Map<String, dynamic>?> getCurrentUserProfile() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return null;

      final response = await _supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

      return response;
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }

  // Update user profile
  static Future<Map<String, dynamic>> updateUserProfile({
    String? fullName,
    String? name,
    String? phoneNumber,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    DateTime? dateOfBirth,
    String? gender,
    String? avatarUrl,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {'success': false, 'message': 'User not authenticated'};
      }

      final updateData = <String, dynamic>{};
      
      if (fullName != null) updateData['full_name'] = fullName;
      if (name != null) updateData['name'] = name;
      if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
      if (phone != null) updateData['phone'] = phone;
      if (address != null) updateData['address'] = address;
      if (city != null) updateData['city'] = city;
      if (state != null) updateData['state'] = state;
      if (country != null) updateData['country'] = country;
      if (postalCode != null) updateData['postal_code'] = postalCode;
      if (dateOfBirth != null) updateData['date_of_birth'] = dateOfBirth.toIso8601String();
      if (gender != null) updateData['gender'] = gender;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

      // Always update the updated_at timestamp
      updateData['updated_at'] = DateTime.now().toIso8601String();

      await _supabase
          .from('users')
          .update(updateData)
          .eq('id', user.id);

      return {'success': true, 'message': 'Profile updated successfully'};
    } catch (e) {
      print('Error updating user profile: $e');
      return {'success': false, 'message': 'Failed to update profile: ${e.toString()}'};
    }
  }

  // Get user's display location (city, state/country)
  static Future<String> getUserLocation() async {
    try {
      final profile = await getCurrentUserProfile();
      if (profile == null) return 'Location not set';

      final city = profile['city'];
      final state = profile['state'];
      final country = profile['country'];

      if (city != null && state != null) {
        return '$city, $state';
      } else if (city != null && country != null) {
        return '$city, $country';
      } else if (city != null) {
        return city;
      } else if (country != null) {
        return country;
      } else {
        return 'Location not set';
      }
    } catch (e) {
      print('Error getting user location: $e');
      return 'Location not available';
    }
  }

  // Get user's display name
  static Future<String> getUserDisplayName() async {
    try {
      final profile = await getCurrentUserProfile();
      if (profile == null) return 'User';

      return profile['name'] ?? 
             profile['full_name'] ?? 
             'User';
    } catch (e) {
      print('Error getting user display name: $e');
      return 'User';
    }
  }

  // Update user location specifically
  static Future<Map<String, dynamic>> updateUserLocation({
    required String city,
    String? state,
    String? country,
    String? postalCode,
    String? address,
  }) async {
    return await updateUserProfile(
      city: city,
      state: state,
      country: country,
      postalCode: postalCode,
      address: address,
    );
  }

  // Check if user profile is complete
  static Future<bool> isProfileComplete() async {
    try {
      final profile = await getCurrentUserProfile();
      if (profile == null) return false;

      // Check if essential fields are filled
      final hasName = profile['name'] != null || profile['full_name'] != null;
      final hasPhone = profile['phone_number'] != null || profile['phone'] != null;
      
      return hasName && hasPhone;
    } catch (e) {
      print('Error checking profile completeness: $e');
      return false;
    }
  }

  // Get user statistics (for admin or analytics)
  static Future<Map<String, dynamic>> getUserStats() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return {};

      final profile = await getCurrentUserProfile();
      if (profile == null) return {};

      return {
        'user_id': user.id,
        'email': user.email,
        'created_at': profile['created_at'],
        'last_login': profile['last_login'],
        'profile_complete': await isProfileComplete(),
        'has_location': profile['city'] != null,
        'has_phone': profile['phone_number'] != null || profile['phone'] != null,
      };
    } catch (e) {
      print('Error getting user stats: $e');
      return {};
    }
  }

  // Delete user account (soft delete by changing status)
  static Future<Map<String, dynamic>> deactivateAccount() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {'success': false, 'message': 'User not authenticated'};
      }

      await _supabase
          .from('users')
          .update({
            'status': 'inactive',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id);

      return {'success': true, 'message': 'Account deactivated successfully'};
    } catch (e) {
      print('Error deactivating account: $e');
      return {'success': false, 'message': 'Failed to deactivate account'};
    }
  }

  // Reactivate user account
  static Future<Map<String, dynamic>> reactivateAccount() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {'success': false, 'message': 'User not authenticated'};
      }

      await _supabase
          .from('users')
          .update({
            'status': 'active',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id);

      return {'success': true, 'message': 'Account reactivated successfully'};
    } catch (e) {
      print('Error reactivating account: $e');
      return {'success': false, 'message': 'Failed to reactivate account'};
    }
  }
}
