-- Check your current category data
-- Run this in your Supabase SQL Editor

-- 1. Check all categories in your table
SELECT 
  id, 
  name, 
  image, 
  featured, 
  status, 
  priority,
  start_time,
  end_time,
  created_at,
  updated_at
FROM category 
ORDER BY priority ASC NULLS LAST, name ASC;

-- 2. Count categories by status
SELECT 
  status, 
  COUNT(*) as count
FROM category 
GROUP BY status;

-- 3. Check for any NULL or empty values
SELECT 
  'id' as field, COUNT(*) as total, COUNT(id) as not_null FROM category
UNION ALL
SELECT 
  'name' as field, COUNT(*) as total, COUNT(name) as not_null FROM category
UNION ALL
SELECT 
  'status' as field, COUNT(*) as total, COUNT(status) as not_null FROM category
UNION ALL
SELECT 
  'priority' as field, COUNT(*) as total, COUNT(priority) as not_null FROM category;

-- 4. If you need to insert some test data, uncomment and run this:
/*
INSERT INTO category (id, name, image, featured, status, priority, created_at, updated_at) VALUES
(1, 'Cleaning', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=150&h=150&fit=crop', true, 'active', 1, NOW(), NOW()),
(2, 'Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=150&h=150&fit=crop', true, 'active', 2, NOW(), NOW()),
(3, 'Electrical', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=150&h=150&fit=crop', true, 'active', 3, NOW(), NOW()),
(4, 'Repairing', 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=150&h=150&fit=crop', true, 'active', 4, NOW(), NOW()),
(5, 'Carpentry', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=150&h=150&fit=crop', false, 'active', 5, NOW(), NOW()),
(6, 'Painting', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=150&h=150&fit=crop', false, 'active', 6, NOW(), NOW()),
(7, 'Moving', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=150&h=150&fit=crop', false, 'active', 7, NOW(), NOW()),
(8, 'Handyman', 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=150&h=150&fit=crop', false, 'active', 8, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  image = EXCLUDED.image,
  featured = EXCLUDED.featured,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  updated_at = NOW();
*/
