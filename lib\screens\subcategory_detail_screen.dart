import 'package:flutter/material.dart';
import '../models/subcategory.dart';
import '../models/product.dart';
import '../services/category_service.dart';
import 'product_detail_screen.dart';

class SubcategoryDetailScreen extends StatefulWidget {
  final Subcategory subcategory;

  const SubcategoryDetailScreen({super.key, required this.subcategory});

  @override
  State<SubcategoryDetailScreen> createState() =>
      _SubcategoryDetailScreenState();
}

class _SubcategoryDetailScreenState extends State<SubcategoryDetailScreen> {
  List<Product> _products = [];
  bool _isLoading = true;
  String selectedFilter = 'All';
  final List<String> filters = ['All', 'Repairing', 'Electrical', 'Plumbing'];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final products = await CategoryService.getProductsBySubcategory(
        widget.subcategory.id?.toString() ?? '',
      );

      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  List<Product> get filteredProducts {
    if (selectedFilter == 'All') return _products;
    return _products
        .where(
          (product) => product.displayName.toLowerCase().contains(
            selectedFilter.toLowerCase(),
          ),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Most Popular Services',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.black),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onPressed: () {},
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Filter chips
                Container(
                  height: 50,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: filters.length,
                    itemBuilder: (context, index) {
                      final filter = filters[index];
                      final isSelected = selectedFilter == filter;
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(filter),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              selectedFilter = filter;
                            });
                          },
                          backgroundColor: Colors.grey[100],
                          selectedColor: const Color(0xFF6B46C1),
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                            side: BorderSide.none,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                // Services list
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = filteredProducts[index];
                      return _buildServiceCard(product);
                    },
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildServiceCard(Product product) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(product: product),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Service provider image
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: _getGradientForService(product.displayName),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: product.image != null && product.image!.isNotEmpty
                    ? Image.network(
                        product.image!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildDefaultServiceIcon(product.displayName),
                      )
                    : _buildDefaultServiceIcon(product.displayName),
              ),
            ),
            const SizedBox(width: 16),
            // Service details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getProviderName(product.displayName),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    product.displayName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    product.formattedPrice,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '4.9 (120 Reviews)',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Bookmark icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _isHighPriority(product.displayName) 
                    ? Colors.red 
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.bookmark_outline,
                size: 20,
                color: _isHighPriority(product.displayName) 
                    ? Colors.white 
                    : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  LinearGradient _getGradientForService(String serviceName) {
    final name = serviceName.toLowerCase();
    if (name.contains('plumbing')) {
      return const LinearGradient(
        colors: [Color(0xFF8B5CF6), Color(0xFFA78BFA)],
      );
    } else if (name.contains('electrical')) {
      return const LinearGradient(
        colors: [Color(0xFFF59E0B), Color(0xFFFBBF24)],
      );
    } else if (name.contains('cleaning')) {
      return const LinearGradient(
        colors: [Color(0xFF8B5CF6), Color(0xFFA78BFA)],
      );
    } else if (name.contains('painting')) {
      return const LinearGradient(
        colors: [Color(0xFF6B7280), Color(0xFF9CA3AF)],
      );
    } else if (name.contains('car') || name.contains('repair')) {
      return const LinearGradient(
        colors: [Color(0xFFEA580C), Color(0xFFFB923C)],
      );
    }
    return const LinearGradient(
      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
    );
  }

  Widget _buildDefaultServiceIcon(String serviceName) {
    IconData icon = Icons.build;
    final name = serviceName.toLowerCase();
    
    if (name.contains('plumbing')) {
      icon = Icons.plumbing;
    } else if (name.contains('electrical')) {
      icon = Icons.electrical_services;
    } else if (name.contains('cleaning')) {
      icon = Icons.cleaning_services;
    } else if (name.contains('painting')) {
      icon = Icons.format_paint;
    } else if (name.contains('car')) {
      icon = Icons.car_repair;
    }

    return Container(
      decoration: BoxDecoration(
        gradient: _getGradientForService(serviceName),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Icon(
          icon,
          color: Colors.white,
          size: 30,
        ),
      ),
    );
  }

  String _getProviderName(String serviceName) {
    final providers = [
      'Samantha Ordonez',
      'Cameron Williamson',
      'Geoffrey Matt',
      'Wade Warren',
    ];
    return providers[serviceName.hashCode % providers.length];
  }

  bool _isHighPriority(String serviceName) {
    return serviceName.toLowerCase().contains('plumbing') || 
           serviceName.toLowerCase().contains('painting');
  }
}
