import 'package:flutter/material.dart';
import '../models/subcategory.dart';

class SubcategoryDetailScreen extends StatefulWidget {
  final Subcategory subcategory;

  const SubcategoryDetailScreen({super.key, required this.subcategory});

  @override
  State<SubcategoryDetailScreen> createState() =>
      _SubcategoryDetailScreenState();
}

class _SubcategoryDetailScreenState extends State<SubcategoryDetailScreen> {
  // Sample service data matching the attached image
  final List<Map<String, dynamic>> _services = [
    {
      'providerName': 'Samantha Ordonez',
      'serviceName': 'Plumbing Services',
      'price': '\$50',
      'rating': '4.9 (120 Reviews)',
      'image':
          'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400',
      'isBookmarked': true,
      'backgroundColor': const Color(0xFFE8F5E8),
    },
    {
      'providerName': '<PERSON>',
      'serviceName': 'Electrical Services',
      'price': '\$30',
      'rating': '4.8 (120 Reviews)',
      'image':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'isBookmarked': false,
      'backgroundColor': const Color(0xFFE3F2FD),
    },
    {
      'providerName': 'Samantha Ordonez',
      'serviceName': 'Floor Cleaning',
      'price': '\$40',
      'rating': '4.9 (120 Reviews)',
      'image':
          'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400',
      'isBookmarked': false,
      'backgroundColor': const Color(0xFFE8F5E8),
    },
    {
      'providerName': 'Geoffrey Matt',
      'serviceName': 'Painting House Walls',
      'price': '\$30',
      'rating': '4.9 (120 Reviews)',
      'image':
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      'isBookmarked': true,
      'backgroundColor': const Color(0xFFFFF3E0),
    },
    {
      'providerName': 'Wade Warren',
      'serviceName': 'Repairing',
      'price': '\$20',
      'rating': '4.9 (120 Reviews)',
      'image':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'isBookmarked': false,
      'backgroundColor': const Color(0xFFFFE0B2),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.subcategory.displayName,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _services.length,
        itemBuilder: (context, index) {
          final service = _services[index];
          return _buildServiceCard(service, index);
        },
      ),
    );
  }

  Widget _buildServiceCard(Map<String, dynamic> service, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Service provider image with background
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: service['backgroundColor'],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                service['image'],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: service['backgroundColor'],
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Service details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  service['providerName'],
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  service['serviceName'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  service['price'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.star, size: 16, color: Colors.orange),
                    const SizedBox(width: 4),
                    Text(
                      service['rating'],
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Bookmark icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: service['isBookmarked'] ? Colors.red : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.bookmark_outline,
              size: 20,
              color: service['isBookmarked'] ? Colors.white : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
