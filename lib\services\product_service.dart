import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/product.dart';

class ProductService {
  static SupabaseClient get _supabase => Supabase.instance.client;

  // Get all active products
  static Future<List<Product>> getAllProducts() async {
    try {
      print('🔄 Fetching all products from database...');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching all products: $e');
      return [];
    }
  }

  // Get products by category
  static Future<List<Product>> getProductsByCategory(int categoryId) async {
    try {
      print('🔄 Fetching products for category ID: $categoryId');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('category_id', categoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products for category $categoryId');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching products by category: $e');
      return [];
    }
  }

  // Get products by subcategory
  static Future<List<Product>> getProductsBySubcategory(int subcategoryId) async {
    try {
      print('🔄 Fetching products for subcategory ID: $subcategoryId');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('subcategory_id', subcategoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products for subcategory $subcategoryId');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching products by subcategory: $e');
      return [];
    }
  }

  // Get products by subsubcategory
  static Future<List<Product>> getProductsBySubSubcategory(int subSubcategoryId) async {
    try {
      print('🔄 Fetching products for subsubcategory ID: $subSubcategoryId');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('sub_subcategory_id', subSubcategoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products for subsubcategory $subSubcategoryId');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching products by subsubcategory: $e');
      return [];
    }
  }

  // Get featured products
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      print('🔄 Fetching featured products...');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('status', 'active')
          .eq('featured', true)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} featured products');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching featured products: $e');
      return [];
    }
  }

  // Get product by ID
  static Future<Product?> getProductById(int productId) async {
    try {
      print('🔄 Fetching product with ID: $productId');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('id', productId)
          .single();

      print('✅ Found product: ${response['product_name']}');
      return Product.fromJson(response);
    } catch (e) {
      print('❌ Error fetching product by ID: $e');
      return null;
    }
  }

  // Search products by name
  static Future<List<Product>> searchProducts(String query) async {
    try {
      print('🔄 Searching products with query: "$query"');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('status', 'active')
          .ilike('product_name', '%$query%')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products matching "$query"');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error searching products: $e');
      return [];
    }
  }

  // Get products with discounts
  static Future<List<Product>> getDiscountedProducts() async {
    try {
      print('🔄 Fetching discounted products...');
      final response = await _supabase
          .from('Products')
          .select()
          .eq('status', 'active')
          .or('discount_percentage.gt.0,flat_discount_amount.gt.0')
          .order('discount_percentage', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} discounted products');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching discounted products: $e');
      return [];
    }
  }

  // Get products by price range
  static Future<List<Product>> getProductsByPriceRange({
    int? minPrice,
    int? maxPrice,
  }) async {
    try {
      print('🔄 Fetching products in price range: ₹$minPrice - ₹$maxPrice');
      
      var query = _supabase
          .from('Products')
          .select()
          .eq('status', 'active');

      if (minPrice != null) {
        query = query.gte('price', minPrice);
      }
      if (maxPrice != null) {
        query = query.lte('price', maxPrice);
      }

      final response = await query
          .order('price', ascending: true)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products in price range');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching products by price range: $e');
      return [];
    }
  }

  // Get products by duration range
  static Future<List<Product>> getProductsByDuration({
    int? minDuration,
    int? maxDuration,
  }) async {
    try {
      print('🔄 Fetching products by duration: ${minDuration}min - ${maxDuration}min');
      
      var query = _supabase
          .from('Products')
          .select()
          .eq('status', 'active');

      if (minDuration != null) {
        query = query.gte('duration_minutes', minDuration);
      }
      if (maxDuration != null) {
        query = query.lte('duration_minutes', maxDuration);
      }

      final response = await query
          .order('duration_minutes', ascending: true)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products by duration');
      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error fetching products by duration: $e');
      return [];
    }
  }

  // Get products with full hierarchy information
  static Future<List<Map<String, dynamic>>> getProductsWithHierarchy() async {
    try {
      print('🔄 Fetching products with full hierarchy...');
      final response = await _supabase
          .from('Products')
          .select('''
            *,
            category:category_id(id, name, image),
            subcategory:subcategory_id(id, name, image),
            subsubcategory:sub_subcategory_id(id, name, image)
          ''')
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      print('✅ Found ${response.length} products with hierarchy');
      return response;
    } catch (e) {
      print('❌ Error fetching products with hierarchy: $e');
      return [];
    }
  }

  // Get product statistics
  static Future<Map<String, dynamic>> getProductStats() async {
    try {
      print('🔄 Fetching product statistics...');
      
      // Get total products count
      final totalResponse = await _supabase
          .from('Products')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('status', 'active');

      // Get featured products count
      final featuredResponse = await _supabase
          .from('Products')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('status', 'active')
          .eq('featured', true);

      // Get discounted products count
      final discountedResponse = await _supabase
          .from('Products')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('status', 'active')
          .or('discount_percentage.gt.0,flat_discount_amount.gt.0');

      final stats = {
        'total_products': totalResponse.count ?? 0,
        'featured_products': featuredResponse.count ?? 0,
        'discounted_products': discountedResponse.count ?? 0,
      };

      print('✅ Product stats: $stats');
      return stats;
    } catch (e) {
      print('❌ Error fetching product stats: $e');
      return {
        'total_products': 0,
        'featured_products': 0,
        'discounted_products': 0,
      };
    }
  }
}
