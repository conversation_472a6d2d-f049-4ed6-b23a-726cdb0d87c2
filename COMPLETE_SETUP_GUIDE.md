# 🚀 DODOBOOKER - Complete Setup Guide

## ✅ What's Already Done

1. **Supabase Configuration**: Connected to your Supabase project
2. **Users Table**: Already created in your database
3. **Authentication**: Integrated with Supabase Auth
4. **Modern UI**: Beautiful home page with categories

## 📋 Next Steps to Complete Setup

### Step 1: Create Required Database Tables

Run these SQL queries in your Supabase SQL Editor:

```sql
-- 1. Create categories table
CREATE TABLE public.categories (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  priority integer DEFAULT 0,
  start_time time without time zone DEFAULT '09:00:00'::time without time zone,
  end_time time without time zone DEFAULT '17:00:00'::time without time zone,
  CONSTRAINT categories_pkey PRIMARY KEY (id),
  CONSTRAINT categories_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text]))
);

-- 2. Create subcategories table
CREATE TABLE public.subcategories (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status character varying(20) DEFAULT null::character varying,
  CONSTRAINT subcategories_pkey PRIMARY KEY (id),
  CONSTRAINT subcategories_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  CONSTRAINT subcategories_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text]))
);

-- 3. Create subsubcategories table
CREATE TABLE public.subsubcategories (
  id bigserial NOT NULL,
  name character varying(255) NOT NULL,
  image character varying(255),
  featured boolean DEFAULT false,
  status character varying(50) DEFAULT 'active'::character varying,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  previous_status character varying(20) DEFAULT null::character varying,
  CONSTRAINT subsubcategories_pkey PRIMARY KEY (id),
  CONSTRAINT unique_subsubcategory_name_per_subcategory UNIQUE (name, subcategory_id),
  CONSTRAINT fk_category FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  CONSTRAINT fk_subcategory FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE
);

-- 4. Create products table
CREATE TABLE public.products (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  product_name text NOT NULL,
  category_id bigint NOT NULL,
  subcategory_id bigint,
  sub_subcategory_id bigint,
  status text DEFAULT 'active'::text,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  price numeric(10, 2) NOT NULL,
  description text,
  image text,
  featured boolean DEFAULT false,
  previous_status character varying(20),
  duration_minutes integer DEFAULT 60,
  number_of_slots integer DEFAULT 0,
  disabled_start_at timestamp with time zone,
  disabled_end_at timestamp with time zone,
  discount_percentage numeric(5, 2) DEFAULT 0,
  discount_type text DEFAULT 'percentage'::text,
  flat_discount_amount numeric(10, 2) DEFAULT 0,
  after_discount_price numeric GENERATED ALWAYS AS (
    CASE
      WHEN (discount_type = 'percentage'::text) THEN round(
        (price - ((price * discount_percentage) / (100)::numeric)), 2
      )
      WHEN (discount_type = 'flat'::text) THEN round((price - flat_discount_amount), 2)
      ELSE price
    END
  ) STORED,
  CONSTRAINT products_pkey PRIMARY KEY (id),
  CONSTRAINT fk_category FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  CONSTRAINT fk_sub_subcategory FOREIGN KEY (sub_subcategory_id) REFERENCES subsubcategories (id) ON DELETE CASCADE,
  CONSTRAINT fk_subcategory FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE,
  CONSTRAINT products_price_check CHECK ((price >= (0)::numeric)),
  CONSTRAINT products_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text]))
);

-- 5. Create coupons table
CREATE TABLE public.coupons (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  code text NOT NULL UNIQUE,
  name text NOT NULL,
  description text,
  discount_type text DEFAULT 'percentage'::text,
  discount_value numeric(10, 2) NOT NULL,
  min_order_amount numeric(10, 2) DEFAULT 0,
  max_discount_amount numeric(10, 2),
  usage_limit integer,
  used_count integer DEFAULT 0,
  status text DEFAULT 'active'::text,
  valid_from timestamp with time zone DEFAULT timezone('utc'::text, now()),
  valid_until timestamp with time zone,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  CONSTRAINT coupons_pkey PRIMARY KEY (id),
  CONSTRAINT coupons_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'expired'::text]))
);

-- 6. Create coupon_subcategory table
CREATE TABLE public.coupon_subcategory (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  coupon_id uuid NOT NULL,
  subcategory_id bigint NOT NULL,
  CONSTRAINT coupon_subcategory_pkey PRIMARY KEY (id),
  CONSTRAINT coupon_subcategory_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES coupons (id) ON DELETE CASCADE,
  CONSTRAINT coupon_subcategory_subcategory_id_fkey FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE
);

-- 7. Create coupon_subsubcategory table
CREATE TABLE public.coupon_subsubcategory (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  coupon_id uuid NOT NULL,
  subsubcategory_id bigint NOT NULL,
  CONSTRAINT coupon_subsubcategory_pkey PRIMARY KEY (id),
  CONSTRAINT coupon_subsubcategory_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES coupons (id) ON DELETE CASCADE,
  CONSTRAINT coupon_subsubcategory_subsubcategory_id_fkey FOREIGN KEY (subsubcategory_id) REFERENCES subsubcategories (id) ON DELETE CASCADE
);
```

### Step 2: Insert Sample Data

```sql
-- Insert sample categories
INSERT INTO categories (name, image, featured, status, priority, start_time, end_time) VALUES
('Carpentry', null, true, 'active', 1, '09:00:00', '17:00:00'),
('Plumbing', null, true, 'active', 2, '08:00:00', '18:00:00'),
('Electricals', null, false, 'active', 3, '09:00:00', '17:00:00'),
('Appliances', null, false, 'active', 4, '10:00:00', '16:00:00'),
('Pestcare', null, true, 'active', 5, '09:00:00', '17:00:00'),
('Cleaning', null, true, 'active', 6, '08:00:00', '20:00:00'),
('Gardening', null, false, 'active', 7, '07:00:00', '17:00:00'),
('Glass Shine', null, false, 'active', 8, '09:00:00', '17:00:00');

-- Insert sample subcategories
INSERT INTO subcategories (name, image, featured, status, category_id) VALUES
('Furniture Repair', null, true, 'active', 1),
('Door Installation', null, false, 'active', 1),
('Pipe Repair', null, true, 'active', 2),
('Bathroom Fitting', null, true, 'active', 2),
('House Cleaning', null, true, 'active', 6),
('Deep Cleaning', null, true, 'active', 6);

-- Insert sample products
INSERT INTO products (product_name, category_id, subcategory_id, status, price, description, featured, duration_minutes, discount_percentage) VALUES
('Furniture Assembly', 1, 1, 'active', 500.00, 'Professional furniture assembly service', true, 120, 10.0),
('Door Repair', 1, 2, 'active', 800.00, 'Complete door repair and maintenance', false, 90, 0.0),
('Pipe Leak Repair', 2, 1, 'active', 300.00, 'Quick pipe leak repair service', true, 60, 20.0),
('Bathroom Installation', 2, 2, 'active', 1500.00, 'Complete bathroom fitting', true, 180, 5.0),
('Regular House Cleaning', 6, 1, 'active', 600.00, 'Regular house cleaning for 2-3 BHK', true, 120, 0.0),
('Deep Cleaning Service', 6, 2, 'active', 1200.00, 'Comprehensive deep cleaning', true, 240, 25.0);
```

### Step 3: Set Up Authentication Trigger

```sql
-- Create trigger function for new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.users (
    id, email, full_name, name, phone_number, phone, status, created_at, updated_at
  )
  VALUES (
    new.id, new.email, new.raw_user_meta_data->>'full_name', 
    new.raw_user_meta_data->>'name', new.raw_user_meta_data->>'phone_number',
    new.raw_user_meta_data->>'phone', 'active', now(), now()
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

### Step 4: Test Your App

1. **Run the app**: `flutter run`
2. **Create a new account** on the signup page
3. **Login** with your credentials
4. **Browse categories** on the beautiful home page
5. **View products** and subcategories

## 🎯 Features Working

- ✅ **User Registration & Login** with Supabase
- ✅ **Automatic user data saving** to users table
- ✅ **Beautiful home page** with categories
- ✅ **Category browsing** with products
- ✅ **Search functionality**
- ✅ **Modern UI design**

## 🔧 Troubleshooting

If you encounter any issues:

1. **Check Supabase connection** in the app logs
2. **Verify tables exist** in your Supabase dashboard
3. **Check RLS policies** if data doesn't load
4. **Ensure sample data is inserted** for testing

Your DODOBOOKER app is now ready! 🏠✨
