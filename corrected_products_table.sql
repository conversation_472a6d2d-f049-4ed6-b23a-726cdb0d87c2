-- DOD<PERSON>BOOKER Corrected Products Table
-- Run this in your Supabase SQL Editor

-- 1. First, create the trigger function for updating updated_at column (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Drop existing Products table if it exists (be careful with this!)
-- Uncomment the next line only if you want to recreate the table from scratch
-- DROP TABLE IF EXISTS public."Products" CASCADE;

-- 3. Create the corrected Products table
CREATE TABLE IF NOT EXISTS public."Products" (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  product_name text NOT NULL,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  sub_subcategory_id bigint NOT NULL,
  status text DEFAULT 'active'::text,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  price bigint NOT NULL DEFAULT 0,
  description text NULL,
  image text NULL,
  featured boolean DEFAULT false,
  previous_status text NULL,
  duration_minutes bigint DEFAULT 60,
  number_of_slots bigint DEFAULT 1,
  disabled_start_at timestamp with time zone NULL,
  disabled_end_at timestamp with time zone NULL,
  discount_percentage bigint DEFAULT 0,
  discount_type text DEFAULT 'percentage'::text,
  flat_discount_amount bigint DEFAULT 0,
  after_discount_price double precision NULL,
  
  -- Primary key constraint
  CONSTRAINT products_pkey PRIMARY KEY (id),
  
  -- Foreign key constraint to category table
  CONSTRAINT products_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES public.category (id) ON DELETE CASCADE,
  
  -- Foreign key constraint to subcategory table
  CONSTRAINT products_subcategory_id_fkey 
    FOREIGN KEY (subcategory_id) REFERENCES public."Subcategory" (id) ON DELETE CASCADE,
  
  -- Foreign key constraint to subsubcategory table
  CONSTRAINT products_sub_subcategory_id_fkey 
    FOREIGN KEY (sub_subcategory_id) REFERENCES public."SubSubCategory" (id) ON DELETE CASCADE,
  
  -- Status validation constraint
  CONSTRAINT products_status_check 
    CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text, 'out_of_stock'::text])),
  
  -- Discount type validation constraint
  CONSTRAINT products_discount_type_check 
    CHECK (discount_type = ANY (ARRAY['percentage'::text, 'flat'::text, 'none'::text])),
  
  -- Price validation constraints
  CONSTRAINT products_price_positive CHECK (price >= 0),
  CONSTRAINT products_discount_percentage_valid CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
  CONSTRAINT products_flat_discount_positive CHECK (flat_discount_amount >= 0),
  CONSTRAINT products_duration_positive CHECK (duration_minutes > 0),
  CONSTRAINT products_slots_positive CHECK (number_of_slots > 0),
  
  -- Unique constraint to prevent duplicate product names within the same subsubcategory
  CONSTRAINT unique_product_name_per_subsubcategory UNIQUE (product_name, sub_subcategory_id)
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public."Products" USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_products_subcategory_id ON public."Products" USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_products_sub_subcategory_id ON public."Products" USING btree (sub_subcategory_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON public."Products" USING btree (status);
CREATE INDEX IF NOT EXISTS idx_products_featured ON public."Products" USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_products_price ON public."Products" USING btree (price);
CREATE INDEX IF NOT EXISTS idx_products_product_name ON public."Products" USING btree (product_name);

-- 5. Create trigger for updating updated_at column
DROP TRIGGER IF EXISTS update_products_updated_at ON public."Products";
CREATE TRIGGER update_products_updated_at 
  BEFORE UPDATE ON public."Products" 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 6. Create trigger for calculating after_discount_price
CREATE OR REPLACE FUNCTION calculate_after_discount_price()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate after_discount_price based on discount_type
  IF NEW.discount_type = 'percentage' AND NEW.discount_percentage > 0 THEN
    NEW.after_discount_price = NEW.price * (1 - NEW.discount_percentage / 100.0);
  ELSIF NEW.discount_type = 'flat' AND NEW.flat_discount_amount > 0 THEN
    NEW.after_discount_price = GREATEST(0, NEW.price - NEW.flat_discount_amount);
  ELSE
    NEW.after_discount_price = NEW.price;
  END IF;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS calculate_discount_price ON public."Products";
CREATE TRIGGER calculate_discount_price 
  BEFORE INSERT OR UPDATE ON public."Products" 
  FOR EACH ROW 
  EXECUTE FUNCTION calculate_after_discount_price();

-- 7. Enable Row Level Security (RLS)
ALTER TABLE public."Products" ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policy for public read access
DROP POLICY IF EXISTS "Allow public read access" ON public."Products";
CREATE POLICY "Allow public read access" ON public."Products"
  FOR SELECT USING (true);

-- 9. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public."Products" TO anon, authenticated;

-- 10. Insert sample products data
-- First, let's check if we have subsubcategories to link to
DO $$
BEGIN
  -- Only insert sample data if subsubcategories exist
  IF EXISTS (SELECT 1 FROM public."SubSubCategory" LIMIT 1) THEN
    
    -- Insert Products for Regular House Cleaning (SubSubCategory ID: 1, Subcategory ID: 1, Category ID: 1)
    INSERT INTO public."Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
    ('Basic House Cleaning - 1 Bedroom', 1, 1, 1, 2500, 'Complete cleaning of 1-bedroom apartment including kitchen, bathroom, living room, and bedroom', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 120, 2, 10),
    ('Basic House Cleaning - 2 Bedroom', 1, 1, 1, 3500, 'Complete cleaning of 2-bedroom apartment including kitchen, bathroom, living room, and bedrooms', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 180, 2, 15),
    ('Basic House Cleaning - 3 Bedroom', 1, 1, 1, 4500, 'Complete cleaning of 3-bedroom house including kitchen, bathrooms, living room, and bedrooms', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', false, 240, 3, 20)
    ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

    -- Insert Products for One-Time House Cleaning (SubSubCategory ID: 2, Subcategory ID: 1, Category ID: 1)
    INSERT INTO public."Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_type, flat_discount_amount) VALUES
    ('One-Time Deep Clean - Small Home', 1, 1, 2, 5000, 'Intensive one-time cleaning for small homes up to 1000 sq ft', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 300, 3, 'flat', 500),
    ('One-Time Deep Clean - Medium Home', 1, 1, 2, 7500, 'Intensive one-time cleaning for medium homes 1000-2000 sq ft', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', true, 420, 4, 'flat', 750),
    ('One-Time Deep Clean - Large Home', 1, 1, 2, 10000, 'Intensive one-time cleaning for large homes over 2000 sq ft', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', false, 540, 5, 'flat', 1000)
    ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

    -- Insert Products for Burst Pipe Repair (SubSubCategory ID: 16, Subcategory ID: 7, Category ID: 2)
    INSERT INTO public."Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
    ('Emergency Burst Pipe Repair', 2, 7, 16, 8000, '24/7 emergency burst pipe repair service with immediate response', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', true, 180, 1, 0),
    ('Burst Pipe Repair with Replacement', 2, 7, 16, 12000, 'Complete burst pipe repair including pipe replacement and testing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', true, 240, 1, 5),
    ('Minor Burst Pipe Fix', 2, 7, 16, 5000, 'Quick fix for minor burst pipe issues', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', false, 90, 1, 10)
    ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

    -- Insert Products for Kitchen Drain Cleaning (SubSubCategory ID: 20, Subcategory ID: 8, Category ID: 2)
    INSERT INTO public."Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
    ('Kitchen Sink Drain Cleaning', 2, 8, 20, 3000, 'Professional kitchen sink drain cleaning and unclogging', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', true, 60, 2, 15),
    ('Kitchen Grease Trap Cleaning', 2, 8, 20, 4500, 'Deep cleaning of kitchen grease traps and drain lines', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', false, 90, 1, 10),
    ('Kitchen Drain Maintenance', 2, 8, 20, 2000, 'Regular maintenance cleaning for kitchen drains', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=300&fit=crop', false, 45, 3, 20)
    ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

    -- Insert Products for IKEA Furniture Assembly (SubSubCategory ID: 28, Subcategory ID: 19, Category ID: 4)
    INSERT INTO public."Products" (product_name, category_id, subcategory_id, sub_subcategory_id, price, description, image, featured, duration_minutes, number_of_slots, discount_percentage) VALUES
    ('IKEA Bed Assembly', 4, 19, 28, 2500, 'Professional assembly of IKEA beds including all types and sizes', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', true, 90, 2, 10),
    ('IKEA Wardrobe Assembly', 4, 19, 28, 4000, 'Complete assembly of IKEA wardrobes and closet systems', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', true, 150, 2, 15),
    ('IKEA Kitchen Cabinet Assembly', 4, 19, 28, 6000, 'Professional assembly of IKEA kitchen cabinets and installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', false, 240, 2, 20),
    ('IKEA Desk Assembly', 4, 19, 28, 1500, 'Quick and efficient assembly of IKEA desks and office furniture', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop', false, 60, 3, 5)
    ON CONFLICT (product_name, sub_subcategory_id) DO NOTHING;

    RAISE NOTICE 'Sample products inserted successfully!';
  ELSE
    RAISE NOTICE 'No subsubcategories found. Please create subsubcategories first before adding products.';
  END IF;
END $$;

-- 11. Verification queries
SELECT 'Products table setup completed successfully!' as message;

-- Check if products were inserted
SELECT 'Products inserted: ' || COUNT(*) as product_count FROM public."Products";

-- Show products with their full hierarchy
SELECT 
  p.id,
  p.product_name,
  p.price,
  p.after_discount_price,
  p.duration_minutes,
  ss.name as subsubcategory_name,
  s.name as subcategory_name,
  c.name as category_name,
  p.status,
  p.featured
FROM public."Products" p
LEFT JOIN public."SubSubCategory" ss ON p.sub_subcategory_id = ss.id
LEFT JOIN public."Subcategory" s ON p.subcategory_id = s.id
LEFT JOIN public.category c ON p.category_id = c.id
ORDER BY c.priority NULLS LAST, s.name, ss.name, p.product_name;

-- Check foreign key relationships and product counts
SELECT 
  c.id as category_id,
  c.name as category_name,
  s.id as subcategory_id,
  s.name as subcategory_name,
  ss.id as subsubcategory_id,
  ss.name as subsubcategory_name,
  COUNT(p.id) as product_count
FROM public.category c
LEFT JOIN public."Subcategory" s ON c.id = s.category_id
LEFT JOIN public."SubSubCategory" ss ON s.id = ss.subcategory_id
LEFT JOIN public."Products" p ON ss.id = p.sub_subcategory_id
GROUP BY c.id, c.name, s.id, s.name, ss.id, ss.name
HAVING COUNT(p.id) > 0
ORDER BY c.priority NULLS LAST, s.name, ss.name;
