class Category {
  final int id;
  final String? name;
  final String? image;
  final bool? featured;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? priority;
  final String? startTime;
  final String? endTime;

  Category({
    required this.id,
    this.name,
    this.image,
    this.featured,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.priority,
    this.startTime,
    this.endTime,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      image: json['image'] as String?,
      featured: json['featured'] as bool?,
      status: json['status'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      priority: json['priority'] != null
          ? (json['priority'] as num).toInt()
          : null,
      startTime: json['start_time'] as String?,
      endTime: json['end_time'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'featured': featured,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'priority': priority,
      'start_time': startTime,
      'end_time': endTime,
    };
  }

  bool get isActive => status == 'active';
  bool get isAvailable {
    if (!isActive) return false;

    // If no time restrictions, it's available
    if (startTime == null || endTime == null) return true;

    // Check if current time is within service hours
    final now = DateTime.now();
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:00';

    return currentTime.compareTo(startTime!) >= 0 &&
        currentTime.compareTo(endTime!) <= 0;
  }

  String get displayName => name ?? 'Unknown Category';
}
