-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Corrected SubSubCategory Table
-- Run this in your Supabase SQL Editor

-- 1. First, create the trigger function for updating updated_at column (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Drop existing SubSubCategory table if it exists (be careful with this!)
-- Uncomment the next line only if you want to recreate the table from scratch
-- DROP TABLE IF EXISTS public."SubSubCategory" CASCADE;

-- 3. Create the corrected SubSubCategory table
CREATE TABLE IF NOT EXISTS public."SubSubCategory" (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status text NULL,
  
  -- Primary key constraint
  CONSTRAINT subsubcategory_pkey PRIMARY KEY (id),
  
  -- Foreign key constraint to category table
  CONSTRAINT subsubcategory_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES public.category (id) ON DELETE CASCADE,
  
  -- Foreign key constraint to subcategory table
  CONSTRAINT subsubcategory_subcategory_id_fkey 
    FOREIGN KEY (subcategory_id) REFERENCES public."Subcategory" (id) ON DELETE CASCADE,
  
  -- Status validation constraint
  CONSTRAINT subsubcategory_status_check 
    CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text])),
  
  -- Unique constraint to prevent duplicate subsubcategory names within the same subcategory
  CONSTRAINT unique_subsubcategory_name_per_subcategory UNIQUE (name, subcategory_id)
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subsubcategory_category_id ON public."SubSubCategory" USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_subcategory_id ON public."SubSubCategory" USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_status ON public."SubSubCategory" USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_featured ON public."SubSubCategory" USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_name ON public."SubSubCategory" USING btree (name);

-- 5. Create trigger for updating updated_at column
DROP TRIGGER IF EXISTS update_subsubcategory_updated_at ON public."SubSubCategory";
CREATE TRIGGER update_subsubcategory_updated_at 
  BEFORE UPDATE ON public."SubSubCategory" 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 6. Enable Row Level Security (RLS)
ALTER TABLE public."SubSubCategory" ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policy for public read access
DROP POLICY IF EXISTS "Allow public read access" ON public."SubSubCategory";
CREATE POLICY "Allow public read access" ON public."SubSubCategory"
  FOR SELECT USING (true);

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public."SubSubCategory" TO anon, authenticated;

-- 9. Insert sample subsubcategories data
-- First, let's check if we have subcategories to link to
DO $$
BEGIN
  -- Only insert sample data if subcategories exist
  IF EXISTS (SELECT 1 FROM public."Subcategory" LIMIT 1) THEN
    
    -- Insert SubSubCategories for House Cleaning (Subcategory ID: 1, Category ID: 1)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('Regular House Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 1),
    ('One-Time House Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 1),
    ('Move-in/Move-out Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1, 1),
    ('Holiday Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1, 1)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    -- Insert SubSubCategories for Office Cleaning (Subcategory ID: 2, Category ID: 1)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('Daily Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1, 2),
    ('Weekly Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1, 2),
    ('Commercial Space Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', false, 'active', 1, 2),
    ('Medical Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', false, 'active', 1, 2)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    -- Insert SubSubCategories for Deep Cleaning (Subcategory ID: 3, Category ID: 1)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('Kitchen Deep Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 3),
    ('Bathroom Deep Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, 3),
    ('Appliance Deep Cleaning', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1, 3)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    -- Insert SubSubCategories for Pipe Repair (Subcategory ID: 7, Category ID: 2)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('Burst Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 7),
    ('Leaky Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 7),
    ('Pipe Replacement', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, 7),
    ('Pipe Insulation', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, 7)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    -- Insert SubSubCategories for Drain Cleaning (Subcategory ID: 8, Category ID: 2)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('Kitchen Drain Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 8),
    ('Bathroom Drain Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, 8),
    ('Main Sewer Line Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, 8),
    ('Floor Drain Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2, 8)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    -- Insert SubSubCategories for Wiring Installation (Subcategory ID: 13, Category ID: 3)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('Home Wiring Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3, 13),
    ('Office Wiring Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3, 13),
    ('Outdoor Wiring Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3, 13)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    -- Insert SubSubCategories for Furniture Assembly (Subcategory ID: 19, Category ID: 4)
    INSERT INTO public."SubSubCategory" (name, image, featured, status, category_id, subcategory_id) VALUES
    ('IKEA Furniture Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4, 19),
    ('Bed Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4, 19),
    ('Desk Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4, 19),
    ('Wardrobe Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4, 19)
    ON CONFLICT (name, subcategory_id) DO NOTHING;

    RAISE NOTICE 'Sample subsubcategories inserted successfully!';
  ELSE
    RAISE NOTICE 'No subcategories found. Please create subcategories first before adding subsubcategories.';
  END IF;
END $$;

-- 10. Verification queries
SELECT 'SubSubCategory table setup completed successfully!' as message;

-- Check if subsubcategories were inserted
SELECT 'SubSubCategories inserted: ' || COUNT(*) as subsubcategory_count FROM public."SubSubCategory";

-- Show subsubcategories with their category and subcategory names
SELECT 
  ss.id,
  ss.name as subsubcategory_name,
  s.name as subcategory_name,
  c.name as category_name,
  ss.status,
  ss.featured
FROM public."SubSubCategory" ss
LEFT JOIN public."Subcategory" s ON ss.subcategory_id = s.id
LEFT JOIN public.category c ON ss.category_id = c.id
ORDER BY c.priority NULLS LAST, s.name, ss.name;

-- Check foreign key relationships
SELECT 
  c.id as category_id,
  c.name as category_name,
  s.id as subcategory_id,
  s.name as subcategory_name,
  COUNT(ss.id) as subsubcategory_count
FROM public.category c
LEFT JOIN public."Subcategory" s ON c.id = s.category_id
LEFT JOIN public."SubSubCategory" ss ON s.id = ss.subcategory_id
GROUP BY c.id, c.name, s.id, s.name
HAVING COUNT(ss.id) > 0
ORDER BY c.priority NULLS LAST, s.name;
