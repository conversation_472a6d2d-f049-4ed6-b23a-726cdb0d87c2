-- DODOBOOKER Sample Categories and Subcategories Data
-- Run this AFTER creating the database structure

-- 1. Insert Categories
INSERT INTO public.category (id, name, image, featured, status, priority, start_time, end_time) VALUES
(1, 'Cleaning Services', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1, '08:00:00', '20:00:00'),
(2, 'Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2, '09:00:00', '18:00:00'),
(3, 'Electrical Services', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3, '09:00:00', '17:00:00'),
(4, 'Carpentry', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4, '08:00:00', '18:00:00'),
(5, 'Painting', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', false, 'active', 5, '08:00:00', '17:00:00'),
(6, 'Appliance Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 6, '10:00:00', '19:00:00'),
(7, 'Gardening', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop', false, 'active', 7, '07:00:00', '18:00:00'),
(8, 'Pest Control', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 8, '09:00:00', '17:00:00')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  image = EXCLUDED.image,
  featured = EXCLUDED.featured,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  start_time = EXCLUDED.start_time,
  end_time = EXCLUDED.end_time,
  updated_at = NOW();

-- 2. Insert Subcategories for Cleaning Services (Category ID: 1)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('House Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1),
('Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1),
('Deep Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1),
('Carpet Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1),
('Window Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1),
('Post-Construction Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1);

-- 3. Insert Subcategories for Plumbing (Category ID: 2)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
('Drain Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
('Faucet Installation', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
('Toilet Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2),
('Water Heater Service', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2),
('Emergency Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2);

-- 4. Insert Subcategories for Electrical Services (Category ID: 3)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Wiring Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3),
('Light Fixture Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3),
('Outlet Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
('Circuit Breaker Repair', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
('Electrical Inspection', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
('Emergency Electrical', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3);

-- 5. Insert Subcategories for Carpentry (Category ID: 4)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Furniture Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4),
('Cabinet Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4),
('Door Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
('Window Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
('Custom Woodwork', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
('Deck Building', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4);

-- 6. Insert Subcategories for Painting (Category ID: 5)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Interior Painting', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', true, 'active', 5),
('Exterior Painting', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', true, 'active', 5),
('Wall Preparation', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', false, 'active', 5),
('Ceiling Painting', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', false, 'active', 5),
('Decorative Painting', 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=300&h=300&fit=crop', false, 'active', 5);

-- 7. Insert Subcategories for Appliance Repair (Category ID: 6)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Refrigerator Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 6),
('Washing Machine Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 6),
('Dishwasher Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 6),
('Oven Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 6),
('Air Conditioner Repair', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 6);

-- 8. Insert Subcategories for Gardening (Category ID: 7)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Lawn Mowing', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop', true, 'active', 7),
('Garden Design', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop', false, 'active', 7),
('Tree Trimming', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop', false, 'active', 7),
('Landscaping', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop', false, 'active', 7),
('Plant Care', 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop', false, 'active', 7);

-- 9. Insert Subcategories for Pest Control (Category ID: 8)
INSERT INTO public.subcategory (name, image, featured, status, category_id) VALUES
('Termite Control', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 8),
('Rodent Control', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 8),
('Insect Control', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 8),
('Fumigation', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 8);

-- 10. Verify the data was inserted correctly
SELECT 'Data insertion completed!' as message;

-- Check categories count
SELECT 'Categories inserted: ' || COUNT(*) as category_count FROM public.category;

-- Check subcategories count
SELECT 'Subcategories inserted: ' || COUNT(*) as subcategory_count FROM public.subcategory;

-- Show categories with their subcategory counts
SELECT 
  c.id,
  c.name as category_name,
  COUNT(s.id) as subcategory_count
FROM public.category c
LEFT JOIN public.subcategory s ON c.id = s.category_id
GROUP BY c.id, c.name
ORDER BY c.priority, c.name;
