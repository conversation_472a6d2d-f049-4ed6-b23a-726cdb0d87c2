-- DODOBOOKER Corrected Database Structure
-- Run this in your Supabase SQL Editor

-- 1. First, create the trigger function for updating updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Create/Update categories table (corrected structure)
CREATE TABLE IF NOT EXISTS public.category (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  priority integer DEFAULT 0,
  start_time time without time zone DEFAULT '09:00:00'::time without time zone,
  end_time time without time zone DEFAULT '17:00:00'::time without time zone,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  CONSTRAINT category_pkey PRIMARY KEY (id),
  CONSTRAINT category_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text]))
);

-- 3. Create subcategories table (corrected structure)
CREATE TABLE IF NOT EXISTS public.subcategory (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status text NULL,
  CONSTRAINT subcategory_pkey PRIMARY KEY (id),
  CONSTRAINT subcategory_category_id_fkey FOREIGN KEY (category_id) REFERENCES category (id) ON DELETE CASCADE,
  CONSTRAINT subcategory_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text])),
  CONSTRAINT unique_subcategory_name_per_category UNIQUE (name, category_id)
);

-- 4. Create subsubcategories table (corrected structure)
CREATE TABLE IF NOT EXISTS public.subsubcategory (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status text NULL,
  CONSTRAINT subsubcategory_pkey PRIMARY KEY (id),
  CONSTRAINT subsubcategory_category_id_fkey FOREIGN KEY (category_id) REFERENCES category (id) ON DELETE CASCADE,
  CONSTRAINT subsubcategory_subcategory_id_fkey FOREIGN KEY (subcategory_id) REFERENCES subcategory (id) ON DELETE CASCADE,
  CONSTRAINT subsubcategory_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text])),
  CONSTRAINT unique_subsubcategory_name_per_subcategory UNIQUE (name, subcategory_id)
);

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_category_status ON public.category USING btree (status);
CREATE INDEX IF NOT EXISTS idx_category_featured ON public.category USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_category_priority ON public.category USING btree (priority);

CREATE INDEX IF NOT EXISTS idx_subcategory_category_id ON public.subcategory USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subcategory_status ON public.subcategory USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subcategory_featured ON public.subcategory USING btree (featured);

CREATE INDEX IF NOT EXISTS idx_subsubcategory_category_id ON public.subsubcategory USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_subcategory_id ON public.subsubcategory USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_status ON public.subsubcategory USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subsubcategory_featured ON public.subsubcategory USING btree (featured);

-- 6. Create triggers for updating updated_at column
DROP TRIGGER IF EXISTS update_category_updated_at ON public.category;
CREATE TRIGGER update_category_updated_at 
  BEFORE UPDATE ON public.category 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subcategory_updated_at ON public.subcategory;
CREATE TRIGGER update_subcategory_updated_at 
  BEFORE UPDATE ON public.subcategory 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subsubcategory_updated_at ON public.subsubcategory;
CREATE TRIGGER update_subsubcategory_updated_at 
  BEFORE UPDATE ON public.subsubcategory 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 7. Enable Row Level Security (RLS) for all tables
ALTER TABLE public.category ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subcategory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subsubcategory ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for public read access
-- Categories - allow public read access
DROP POLICY IF EXISTS "Allow public read access" ON public.category;
CREATE POLICY "Allow public read access" ON public.category
  FOR SELECT USING (true);

-- Subcategories - allow public read access
DROP POLICY IF EXISTS "Allow public read access" ON public.subcategory;
CREATE POLICY "Allow public read access" ON public.subcategory
  FOR SELECT USING (true);

-- Subsubcategories - allow public read access
DROP POLICY IF EXISTS "Allow public read access" ON public.subsubcategory;
CREATE POLICY "Allow public read access" ON public.subsubcategory
  FOR SELECT USING (true);

-- 9. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.category TO anon, authenticated;
GRANT SELECT ON public.subcategory TO anon, authenticated;
GRANT SELECT ON public.subsubcategory TO anon, authenticated;

-- Success message
SELECT 'Database structure created successfully!' as message;
