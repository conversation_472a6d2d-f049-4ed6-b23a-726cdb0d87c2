-- DODOBOOKER Cart Table Setup
-- Run this in your Supabase SQL Editor

-- 1. Create cart table
CREATE TABLE IF NOT EXISTS public.cart (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  user_id uuid NOT NULL,
  product_id bigint NOT NULL,
  quantity integer NOT NULL DEFAULT 1,
  price bigint NOT NULL,
  total_price bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  
  -- Primary key constraint
  CONSTRAINT cart_pkey PRIMARY KEY (id),
  
  -- Foreign key constraints
  CONSTRAINT cart_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE,
  CONSTRAINT cart_product_id_fkey FOREIGN KEY (product_id) REFERENCES "Products" (id) ON DELETE CASCADE,
  
  -- Validation constraints
  CONSTRAINT cart_quantity_positive CHECK (quantity > 0),
  CONSTRAINT cart_price_positive CHECK (price >= 0),
  CONSTRAINT cart_total_price_positive CHECK (total_price >= 0),
  
  -- Unique constraint to prevent duplicate products for same user
  CONSTRAINT unique_user_product UNIQUE (user_id, product_id)
);

-- 2. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cart_user_id ON public.cart USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_cart_product_id ON public.cart USING btree (product_id);
CREATE INDEX IF NOT EXISTS idx_cart_created_at ON public.cart USING btree (created_at);

-- 3. Create trigger for updating updated_at column
DROP TRIGGER IF EXISTS update_cart_updated_at ON public.cart;
CREATE TRIGGER update_cart_updated_at 
  BEFORE UPDATE ON public.cart 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 4. Create trigger for calculating total_price
CREATE OR REPLACE FUNCTION calculate_cart_total_price()
RETURNS TRIGGER AS $$
BEGIN
  NEW.total_price = NEW.quantity * NEW.price;
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS calculate_cart_total ON public.cart;
CREATE TRIGGER calculate_cart_total 
  BEFORE INSERT OR UPDATE ON public.cart 
  FOR EACH ROW 
  EXECUTE FUNCTION calculate_cart_total_price();

-- 5. Enable Row Level Security (RLS)
ALTER TABLE public.cart ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies
-- Policy: Users can read their own cart items
DROP POLICY IF EXISTS "Users can read own cart" ON public.cart;
CREATE POLICY "Users can read own cart" ON public.cart
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own cart items
DROP POLICY IF EXISTS "Users can insert own cart" ON public.cart;
CREATE POLICY "Users can insert own cart" ON public.cart
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own cart items
DROP POLICY IF EXISTS "Users can update own cart" ON public.cart;
CREATE POLICY "Users can update own cart" ON public.cart
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own cart items
DROP POLICY IF EXISTS "Users can delete own cart" ON public.cart;
CREATE POLICY "Users can delete own cart" ON public.cart
  FOR DELETE USING (auth.uid() = user_id);

-- 7. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.cart TO authenticated;

-- 8. Create orders table for completed purchases
CREATE TABLE IF NOT EXISTS public.orders (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  user_id uuid NOT NULL,
  order_number text NOT NULL,
  total_amount bigint NOT NULL,
  status text DEFAULT 'pending'::text,
  payment_status text DEFAULT 'pending'::text,
  delivery_address text NULL,
  delivery_date timestamp with time zone NULL,
  notes text NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  
  -- Primary key constraint
  CONSTRAINT orders_pkey PRIMARY KEY (id),
  
  -- Foreign key constraint
  CONSTRAINT orders_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE,
  
  -- Status validation constraints
  CONSTRAINT orders_status_check CHECK (status = ANY (ARRAY['pending'::text, 'confirmed'::text, 'in_progress'::text, 'completed'::text, 'cancelled'::text])),
  CONSTRAINT orders_payment_status_check CHECK (payment_status = ANY (ARRAY['pending'::text, 'paid'::text, 'failed'::text, 'refunded'::text])),
  
  -- Validation constraints
  CONSTRAINT orders_total_amount_positive CHECK (total_amount >= 0),
  CONSTRAINT orders_order_number_unique UNIQUE (order_number)
);

-- 9. Create order_items table for order details
CREATE TABLE IF NOT EXISTS public.order_items (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  order_id bigint NOT NULL,
  product_id bigint NOT NULL,
  quantity integer NOT NULL,
  price bigint NOT NULL,
  total_price bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  
  -- Primary key constraint
  CONSTRAINT order_items_pkey PRIMARY KEY (id),
  
  -- Foreign key constraints
  CONSTRAINT order_items_order_id_fkey FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
  CONSTRAINT order_items_product_id_fkey FOREIGN KEY (product_id) REFERENCES "Products" (id) ON DELETE CASCADE,
  
  -- Validation constraints
  CONSTRAINT order_items_quantity_positive CHECK (quantity > 0),
  CONSTRAINT order_items_price_positive CHECK (price >= 0),
  CONSTRAINT order_items_total_price_positive CHECK (total_price >= 0)
);

-- 10. Create indexes for orders and order_items
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON public.orders USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON public.orders USING btree (status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON public.orders USING btree (created_at);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON public.order_items USING btree (order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON public.order_items USING btree (product_id);

-- 11. Create triggers for orders
DROP TRIGGER IF EXISTS update_orders_updated_at ON public.orders;
CREATE TRIGGER update_orders_updated_at 
  BEFORE UPDATE ON public.orders 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 12. Enable RLS for orders and order_items
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;

-- 13. Create RLS policies for orders
DROP POLICY IF EXISTS "Users can read own orders" ON public.orders;
CREATE POLICY "Users can read own orders" ON public.orders
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own orders" ON public.orders;
CREATE POLICY "Users can insert own orders" ON public.orders
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 14. Create RLS policies for order_items
DROP POLICY IF EXISTS "Users can read own order items" ON public.order_items;
CREATE POLICY "Users can read own order items" ON public.order_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM orders 
      WHERE orders.id = order_items.order_id 
      AND orders.user_id = auth.uid()
    )
  );

-- 15. Grant permissions for orders
GRANT ALL ON public.orders TO authenticated;
GRANT ALL ON public.order_items TO authenticated;

-- Success message
SELECT 'Cart and Orders tables setup completed successfully!' as message;

-- Show table structure
SELECT 'Cart table created with columns:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'cart' AND table_schema = 'public'
ORDER BY ordinal_position;
