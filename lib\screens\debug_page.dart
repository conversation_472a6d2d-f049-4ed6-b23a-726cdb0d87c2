import 'package:flutter/material.dart';
import '../services/category_service.dart';
import '../models/category.dart';
import '../models/subcategory.dart';
import '../models/subsubcategory.dart';
import '../models/product.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  List<Category> _categories = [];
  List<Subcategory> _subcategories = [];
  List<SubSubCategory> _subsubcategories = [];
  List<Product> _products = [];
  bool _isLoading = false;
  String _status = 'Ready to test';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Debug'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status: $_status',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            // Test buttons
            Wrap(
              spacing: 10,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _testConnection,
                  child: const Text('Test Connection'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testCategories,
                  child: const Text('Test Categories'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testSubcategories,
                  child: const Text('Test Subcategories'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testSubsubcategories,
                  child: const Text('Test SubSubCategories'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testProducts,
                  child: const Text('Test Products'),
                ),
              ],
            ),

            const SizedBox(height: 20),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_categories.isNotEmpty) ...[
                        Text(
                          'Categories (${_categories.length}):',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ..._categories.map(
                          (cat) => ListTile(
                            title: Text(cat.displayName),
                            subtitle: Text('ID: ${cat.id}'),
                          ),
                        ),
                        const Divider(),
                      ],

                      if (_subcategories.isNotEmpty) ...[
                        Text(
                          'Subcategories (${_subcategories.length}):',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ..._subcategories.map(
                          (sub) => ListTile(
                            title: Text(sub.displayName),
                            subtitle: Text(
                              'ID: ${sub.id}, Category: ${sub.categoryId}',
                            ),
                          ),
                        ),
                        const Divider(),
                      ],

                      if (_subsubcategories.isNotEmpty) ...[
                        Text(
                          'SubSubCategories (${_subsubcategories.length}):',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ..._subsubcategories.map(
                          (subsub) => ListTile(
                            title: Text(subsub.displayName),
                            subtitle: Text(
                              'ID: ${subsub.id}, Sub: ${subsub.subcategoryId}',
                            ),
                          ),
                        ),
                        const Divider(),
                      ],

                      if (_products.isNotEmpty) ...[
                        Text(
                          'Products (${_products.length}):',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ..._products.map(
                          (prod) => ListTile(
                            title: Text(prod.displayName),
                            subtitle: Text(
                              'ID: ${prod.id}, Price: ${prod.formattedPrice}',
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing database connection...';
    });

    try {
      final supabase = Supabase.instance.client;

      // Test basic connection and get all categories
      final allCategories = await supabase.from('category').select();

      final activeCategories = await supabase
          .from('category')
          .select()
          .eq('status', 'active');

      setState(() {
        _status =
            '''Connection successful!
Total categories: ${allCategories.length}
Active categories: ${activeCategories.length}

All categories: ${allCategories.map((c) => '${c['name']} (${c['status']})').join(', ')}''';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Connection failed: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testCategories() async {
    setState(() {
      _isLoading = true;
      _status = 'Loading categories...';
    });

    try {
      final categories = await CategoryService.getCategories();
      setState(() {
        _categories = categories;
        _status = 'Categories loaded: ${categories.length}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading categories: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSubcategories() async {
    if (_categories.isEmpty) {
      setState(() => _status = 'Load categories first');
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Loading subcategories...';
    });

    try {
      final subcategories = await CategoryService.getSubcategories(
        _categories.first.id!,
      );
      setState(() {
        _subcategories = subcategories;
        _status = 'Subcategories loaded: ${subcategories.length}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading subcategories: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSubsubcategories() async {
    if (_subcategories.isEmpty) {
      setState(() => _status = 'Load subcategories first');
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Loading subsubcategories...';
    });

    try {
      final subsubcategories = await CategoryService.getSubsubcategories(
        _subcategories.first.id!,
      );
      setState(() {
        _subsubcategories = subsubcategories;
        _status = 'SubSubCategories loaded: ${subsubcategories.length}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading subsubcategories: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testProducts() async {
    if (_categories.isEmpty) {
      setState(() => _status = 'Load categories first');
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Loading products...';
    });

    try {
      final products = await CategoryService.getProductsByCategory(
        _categories.first.id!,
      );
      setState(() {
        _products = products;
        _status = 'Products loaded: ${products.length}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading products: $e';
        _isLoading = false;
      });
    }
  }
}
