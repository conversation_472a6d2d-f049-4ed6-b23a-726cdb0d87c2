import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart_item.dart';
import '../models/product.dart';

class CartService {
  static const String _cartKey = 'cart_items';
  static List<CartItem> _cartItems = [];
  static final List<Function()> _listeners = [];

  // Get all cart items
  static List<CartItem> get cartItems => List.unmodifiable(_cartItems);

  // Get cart item count
  static int get itemCount => _cartItems.fold(0, (sum, item) => sum + item.quantity);

  // Get total price
  static double get totalPrice => _cartItems.fold(0.0, (sum, item) => sum + item.totalPrice);

  // Add listener for cart changes
  static void addListener(Function() listener) {
    _listeners.add(listener);
  }

  // Remove listener
  static void removeListener(Function() listener) {
    _listeners.remove(listener);
  }

  // Notify all listeners
  static void _notifyListeners() {
    for (var listener in _listeners) {
      listener();
    }
  }

  // Load cart from storage
  static Future<void> loadCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = prefs.getString(_cartKey);
      
      if (cartJson != null) {
        final List<dynamic> cartList = json.decode(cartJson);
        _cartItems = cartList.map((item) => CartItem.fromJson(item)).toList();
        _notifyListeners();
      }
    } catch (e) {
      print('Error loading cart: $e');
    }
  }

  // Save cart to storage
  static Future<void> _saveCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = json.encode(_cartItems.map((item) => item.toJson()).toList());
      await prefs.setString(_cartKey, cartJson);
    } catch (e) {
      print('Error saving cart: $e');
    }
  }

  // Add item to cart
  static Future<void> addToCart(Product product, {int quantity = 1, String? variant}) async {
    final existingIndex = _cartItems.indexWhere(
      (item) => item.product.id == product.id && item.selectedVariant == variant,
    );

    if (existingIndex >= 0) {
      _cartItems[existingIndex].quantity += quantity;
    } else {
      _cartItems.add(CartItem(
        product: product,
        quantity: quantity,
        selectedVariant: variant,
      ));
    }

    await _saveCart();
    _notifyListeners();
  }

  // Remove item from cart
  static Future<void> removeFromCart(CartItem item) async {
    _cartItems.remove(item);
    await _saveCart();
    _notifyListeners();
  }

  // Update item quantity
  static Future<void> updateQuantity(CartItem item, int newQuantity) async {
    if (newQuantity <= 0) {
      await removeFromCart(item);
      return;
    }

    final index = _cartItems.indexOf(item);
    if (index >= 0) {
      _cartItems[index].quantity = newQuantity;
      await _saveCart();
      _notifyListeners();
    }
  }

  // Clear cart
  static Future<void> clearCart() async {
    _cartItems.clear();
    await _saveCart();
    _notifyListeners();
  }

  // Get cart item for a specific product
  static CartItem? getCartItem(Product product, {String? variant}) {
    try {
      return _cartItems.firstWhere(
        (item) => item.product.id == product.id && item.selectedVariant == variant,
      );
    } catch (e) {
      return null;
    }
  }

  // Check if product is in cart
  static bool isInCart(Product product, {String? variant}) {
    return getCartItem(product, variant: variant) != null;
  }
}
