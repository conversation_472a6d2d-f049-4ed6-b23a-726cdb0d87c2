-- <PERSON><PERSON><PERSON><PERSON><PERSON>ER Corrected Subcategory Table
-- Run this in your Supabase SQL Editor

-- 1. First, create the trigger function for updating updated_at column (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Drop existing Subcategory table if it exists (be careful with this!)
-- Uncomment the next line only if you want to recreate the table from scratch
-- DROP TABLE IF EXISTS public."Subcategory" CASCADE;

-- 3. Create the corrected Subcategory table
CREATE TABLE IF NOT EXISTS public."Subcategory" (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text NULL,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status text NULL,
  
  -- Primary key constraint
  CONSTRAINT subcategory_pkey PRIMARY KEY (id),
  
  -- Foreign key constraint to category table
  CONSTRAINT subcategory_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES public.category (id) ON DELETE CASCADE,
  
  -- Status validation constraint
  CONSTRAINT subcategory_status_check 
    CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'deleted'::text])),
  
  -- Unique constraint to prevent duplicate subcategory names within the same category
  CONSTRAINT unique_subcategory_name_per_category UNIQUE (name, category_id)
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subcategory_category_id ON public."Subcategory" USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subcategory_status ON public."Subcategory" USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subcategory_featured ON public."Subcategory" USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_subcategory_name ON public."Subcategory" USING btree (name);

-- 5. Create trigger for updating updated_at column
DROP TRIGGER IF EXISTS update_subcategory_updated_at ON public."Subcategory";
CREATE TRIGGER update_subcategory_updated_at 
  BEFORE UPDATE ON public."Subcategory" 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 6. Enable Row Level Security (RLS)
ALTER TABLE public."Subcategory" ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policy for public read access
DROP POLICY IF EXISTS "Allow public read access" ON public."Subcategory";
CREATE POLICY "Allow public read access" ON public."Subcategory"
  FOR SELECT USING (true);

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public."Subcategory" TO anon, authenticated;

-- 9. Insert sample subcategories data
-- First, let's check if we have categories to link to
DO $$
BEGIN
  -- Only insert sample data if categories exist
  IF EXISTS (SELECT 1 FROM public.category LIMIT 1) THEN
    
    -- Insert Subcategories for Cleaning Services (Category ID: 1)
    INSERT INTO public."Subcategory" (name, image, featured, status, category_id) VALUES
    ('House Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1),
    ('Office Cleaning', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop', true, 'active', 1),
    ('Deep Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', 1),
    ('Carpet Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1),
    ('Window Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1),
    ('Post-Construction Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', 1)
    ON CONFLICT (name, category_id) DO NOTHING;

    -- Insert Subcategories for Plumbing (Category ID: 2)
    INSERT INTO public."Subcategory" (name, image, featured, status, category_id) VALUES
    ('Pipe Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
    ('Drain Cleaning', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
    ('Faucet Installation', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2),
    ('Toilet Repair', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2),
    ('Water Heater Service', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', false, 'active', 2),
    ('Emergency Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', 2)
    ON CONFLICT (name, category_id) DO NOTHING;

    -- Insert Subcategories for Electrical Services (Category ID: 3)
    INSERT INTO public."Subcategory" (name, image, featured, status, category_id) VALUES
    ('Wiring Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3),
    ('Light Fixture Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3),
    ('Outlet Installation', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
    ('Circuit Breaker Repair', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
    ('Electrical Inspection', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', false, 'active', 3),
    ('Emergency Electrical', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', 3)
    ON CONFLICT (name, category_id) DO NOTHING;

    -- Insert Subcategories for Carpentry (Category ID: 4)
    INSERT INTO public."Subcategory" (name, image, featured, status, category_id) VALUES
    ('Furniture Assembly', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4),
    ('Cabinet Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', true, 'active', 4),
    ('Door Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
    ('Window Installation', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
    ('Custom Woodwork', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4),
    ('Deck Building', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', 4)
    ON CONFLICT (name, category_id) DO NOTHING;

    RAISE NOTICE 'Sample subcategories inserted successfully!';
  ELSE
    RAISE NOTICE 'No categories found. Please create categories first before adding subcategories.';
  END IF;
END $$;

-- 10. Verification queries
SELECT 'Subcategory table setup completed successfully!' as message;

-- Check if subcategories were inserted
SELECT 'Subcategories inserted: ' || COUNT(*) as subcategory_count FROM public."Subcategory";

-- Show subcategories with their category names
SELECT 
  s.id,
  s.name as subcategory_name,
  c.name as category_name,
  s.status,
  s.featured
FROM public."Subcategory" s
LEFT JOIN public.category c ON s.category_id = c.id
ORDER BY c.priority NULLS LAST, s.name;

-- Check foreign key relationships
SELECT 
  c.id as category_id,
  c.name as category_name,
  COUNT(s.id) as subcategory_count
FROM public.category c
LEFT JOIN public."Subcategory" s ON c.id = s.category_id
GROUP BY c.id, c.name
ORDER BY c.priority NULLS LAST;
