class Subcategory {
  final int? id;
  final String? name;
  final String? image;
  final bool? featured;
  final String? status;
  final int? categoryId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? previousStatus;

  Subcategory({
    this.id,
    this.name,
    this.image,
    this.featured,
    this.status,
    this.categoryId,
    this.createdAt,
    this.updatedAt,
    this.previousStatus,
  });

  factory Subcategory.fromJson(Map<String, dynamic> json) {
    return Subcategory(
      id: json['id'] as int?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      featured: json['featured'] as bool?,
      status: json['status'] as String?,
      categoryId: json['category_id'] as int?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      previousStatus: json['previous_status'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'featured': featured,
      'status': status,
      'category_id': categoryId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'previous_status': previousStatus,
    };
  }

  bool get isActive => status == 'active';
  String get displayName => name ?? 'Unknown Subcategory';
}
