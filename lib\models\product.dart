class Product {
  final int? id;
  final String? productName;
  final int? categoryId;
  final int? subcategoryId;
  final int? subSubcategoryId;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? price;
  final String? description;
  final String? image;
  final bool? featured;
  final String? previousStatus;
  final int? durationMinutes;
  final int? numberOfSlots;
  final DateTime? disabledStartAt;
  final DateTime? disabledEndAt;
  final int? discountPercentage;
  final String? discountType;
  final int? flatDiscountAmount;
  final double? afterDiscountPrice;

  Product({
    this.id,
    this.productName,
    this.categoryId,
    this.subcategoryId,
    this.subSubcategoryId,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.price,
    this.description,
    this.image,
    this.featured,
    this.previousStatus,
    this.durationMinutes,
    this.numberOfSlots,
    this.disabledStartAt,
    this.disabledEndAt,
    this.discountPercentage,
    this.discountType,
    this.flatDiscountAmount,
    this.afterDiscountPrice,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as int?,
      productName: json['product_name'] as String?,
      categoryId: json['category_id'] as int?,
      subcategoryId: json['subcategory_id'] as int?,
      subSubcategoryId: json['sub_subcategory_id'] as int?,
      status: json['status'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      price: json['price'] as int?,
      description: json['description'] as String?,
      image: json['image'] as String?,
      featured: json['featured'] as bool?,
      previousStatus: json['previous_status'] as String?,
      durationMinutes: json['duration_minutes'] as int?,
      numberOfSlots: json['number_of_slots'] as int?,
      disabledStartAt: json['disabled_start_at'] != null
          ? DateTime.parse(json['disabled_start_at'] as String)
          : null,
      disabledEndAt: json['disabled_end_at'] != null
          ? DateTime.parse(json['disabled_end_at'] as String)
          : null,
      discountPercentage: json['discount_percentage'] as int?,
      discountType: json['discount_type'] as String?,
      flatDiscountAmount: json['flat_discount_amount'] as int?,
      afterDiscountPrice: json['after_discount_price'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_name': productName,
      'category_id': categoryId,
      'subcategory_id': subcategoryId,
      'sub_subcategory_id': subSubcategoryId,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'price': price,
      'description': description,
      'image': image,
      'featured': featured,
      'previous_status': previousStatus,
      'duration_minutes': durationMinutes,
      'number_of_slots': numberOfSlots,
      'disabled_start_at': disabledStartAt?.toIso8601String(),
      'disabled_end_at': disabledEndAt?.toIso8601String(),
      'discount_percentage': discountPercentage,
      'discount_type': discountType,
      'flat_discount_amount': flatDiscountAmount,
      'after_discount_price': afterDiscountPrice,
    };
  }

  bool get isActive => status == 'active';
  String get displayName => productName ?? 'Unknown Product';
  double get priceAsDouble => (price?.toDouble() ?? 0.0);

  bool get hasDiscount {
    return (discountPercentage != null && discountPercentage! > 0) ||
        (flatDiscountAmount != null && flatDiscountAmount! > 0);
  }

  double get finalPrice {
    if (afterDiscountPrice != null && afterDiscountPrice! > 0) {
      return afterDiscountPrice!;
    }
    return price?.toDouble() ?? 0.0;
  }

  String get formattedPrice {
    return '₹${finalPrice.toStringAsFixed(0)}';
  }

  String? get formattedOriginalPrice {
    if (hasDiscount && price != null) {
      return '₹${price.toString()}';
    }
    return null;
  }

  String? get formattedDuration {
    if (durationMinutes != null) {
      if (durationMinutes! >= 60) {
        final hours = durationMinutes! ~/ 60;
        final minutes = durationMinutes! % 60;
        if (minutes == 0) {
          return '${hours}h';
        } else {
          return '${hours}h ${minutes}m';
        }
      } else {
        return '${durationMinutes}m';
      }
    }
    return null;
  }
}
