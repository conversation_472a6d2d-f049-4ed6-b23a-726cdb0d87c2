import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class AuthService {
  static SupabaseClient get _supabase => Supabase.instance.client;

  // Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
  }

  // Register a new user
  static Future<Map<String, dynamic>> registerUser({
    required String name,
    required String email,
    required String password,
    String? phoneNumber,
  }) async {
    try {
      // Sign up with Supabase Auth with user metadata
      final AuthResponse authResponse = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': name,
          'name': name,
          'phone_number': phoneNumber,
          'phone': phoneNumber,
        },
      );

      if (authResponse.user == null) {
        return {'success': false, 'message': 'Failed to create user account'};
      }

      // For development, we'll treat registration as successful regardless of email confirmation
      // In production, you might want to handle email confirmation differently
      return {
        'success': true,
        'message': 'Account created successfully! You can now sign in.',
        'user': authResponse.user,
        'needsConfirmation': false,
      };
    } catch (e) {
      print('Registration error: $e');
      String errorMessage = 'Registration failed';

      if (e.toString().contains('User already registered')) {
        errorMessage =
            'This email is already registered. Please try logging in.';
      } else if (e.toString().contains('Invalid email')) {
        errorMessage = 'Please enter a valid email address';
      } else if (e.toString().contains(
        'Password should be at least 6 characters',
      )) {
        errorMessage = 'Password must be at least 6 characters long';
      } else if (e.toString().contains('signup_disabled')) {
        errorMessage = 'Registration is currently disabled';
      }

      return {'success': false, 'message': errorMessage};
    }
  }

  // Login user
  static Future<Map<String, dynamic>> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      final AuthResponse authResponse = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        return {'success': false, 'message': 'Invalid email or password'};
      }

      // Update last login time
      try {
        await _supabase
            .from('users')
            .update({
              'last_login': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now(),
            })
            .eq('id', authResponse.user!.id);
      } catch (e) {
        // Continue even if updating last login fails
        print('Failed to update last login: $e');
      }

      // Get user data from users table
      Map<String, dynamic>? userData;
      try {
        userData = await _supabase
            .from('users')
            .select()
            .eq('id', authResponse.user!.id)
            .single();
      } catch (e) {
        // If user data doesn't exist in users table, create it
        userData = {
          'id': authResponse.user!.id,
          'email': authResponse.user!.email,
          'full_name': authResponse.user!.userMetadata?['full_name'],
          'name': authResponse.user!.userMetadata?['name'],
          'phone_number': authResponse.user!.userMetadata?['phone_number'],
          'phone': authResponse.user!.userMetadata?['phone'],
        };
      }

      return {
        'success': true,
        'message': 'Login successful',
        'user': authResponse.user,
        'userData': userData,
      };
    } catch (e) {
      print('Login error: $e');
      String errorMessage = 'Login failed';

      if (e.toString().contains('Invalid login credentials')) {
        errorMessage = 'Invalid email or password';
      } else if (e.toString().contains('Email not confirmed')) {
        errorMessage = 'Please confirm your email before logging in';
      }

      return {'success': false, 'message': errorMessage};
    }
  }

  // Get current logged in user
  static Future<Map<String, dynamic>?> getCurrentUser() async {
    try {
      final User? user = _supabase.auth.currentUser;

      if (user == null) {
        return null;
      }

      // Get user data from users table
      final userData = await _supabase
          .from('users')
          .select()
          .eq('id', user.id)
          .single();

      return {'user': user, 'userData': userData};
    } catch (e) {
      print('Get current user error: $e');
      return null;
    }
  }

  // Logout user
  static Future<Map<String, dynamic>> logoutUser() async {
    try {
      await _supabase.auth.signOut();
      return {'success': true, 'message': 'Logged out successfully'};
    } catch (e) {
      print('Logout error: $e');
      return {'success': false, 'message': 'Logout failed: ${e.toString()}'};
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final user = _supabase.auth.currentUser;
    return user != null;
  }

  // Get current user session
  static Session? getCurrentSession() {
    return _supabase.auth.currentSession;
  }

  // Get all users (for admin purposes)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final response = await _supabase
          .from('users')
          .select()
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Get all users error: $e');
      return [];
    }
  }
}
